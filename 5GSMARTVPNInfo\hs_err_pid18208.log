#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1050416 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=18208, tid=11928
#
# JRE version: Java(TM) SE Runtime Environment (21.0.2+13) (build 21.0.2+13-LTS-58)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Mon Jun  2 12:00:51 2025 Bangladesh Standard Time elapsed time: 26.425434 seconds (0d 0h 0m 26s)

---------------  T H R E A D  ---------------

Current thread (0x000002029b42a310):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=11928, stack(0x0000008961a00000,0x0000008961b00000) (1024K)]


Current CompileTask:
C2:  26425 17595       4       org.gradle.api.internal.artifacts.transform.AbstractTransformExecution::identify (14 bytes)

Stack: [0x0000008961a00000,0x0000008961b00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cade9]
V  [jvm.dll+0x8569c1]
V  [jvm.dll+0x858d2e]
V  [jvm.dll+0x859413]
V  [jvm.dll+0x280e56]
V  [jvm.dll+0xc3f3d]
V  [jvm.dll+0xc4473]
V  [jvm.dll+0x3b5bf2]
V  [jvm.dll+0x1e2013]
V  [jvm.dll+0x249d22]
V  [jvm.dll+0x2491b1]
V  [jvm.dll+0x1c9634]
V  [jvm.dll+0x258859]
V  [jvm.dll+0x256e3a]
V  [jvm.dll+0x3ef6c6]
V  [jvm.dll+0x7ff568]
V  [jvm.dll+0x6c953d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000202e9e39fe0, length=149, elements={
0x00000202fbe664e0, 0x00000202ff4b61a0, 0x00000202ff4b6fd0, 0x00000202ff4b9830,
0x00000202ff4bc3a0, 0x00000202ff4bce00, 0x00000202ff4bf870, 0x000002029b42a310,
0x00000202ff4c57e0, 0x000002029b505670, 0x000002029b6d2fa0, 0x00000202e27b5440,
0x00000202e1f1cc00, 0x00000202e2a04db0, 0x00000202e28c0310, 0x00000202e283c520,
0x00000202e29db2d0, 0x00000202e28a1750, 0x00000202e28a1de0, 0x00000202e289fd10,
0x00000202e28a10c0, 0x00000202e28a2470, 0x00000202e28a0a30, 0x00000202e28a2b00,
0x00000202e28a03a0, 0x00000202e44d7b80, 0x00000202e44d74f0, 0x00000202e44d8210,
0x00000202e44dd0d0, 0x00000202e44da2e0, 0x00000202e44da970, 0x00000202e44db690,
0x00000202e44d6e60, 0x00000202e44dbd20, 0x00000202e44d95c0, 0x00000202e44d9c50,
0x00000202e44dc3b0, 0x00000202e44d67d0, 0x00000202e4d63aa0, 0x00000202e4d65b70,
0x00000202e4d68ff0, 0x00000202e4d675b0, 0x00000202e4d66200, 0x00000202e4d64e50,
0x00000202e4d62060, 0x00000202e4d647c0, 0x00000202e4d654e0, 0x00000202e4d69680,
0x00000202e4d66890, 0x00000202e4d66f20, 0x00000202e4d67c40, 0x00000202e4d626f0,
0x00000202e4d682d0, 0x00000202e4d62d80, 0x00000202e4d63410, 0x00000202e4d68960,
0x00000202e1153030, 0x00000202e1155e20, 0x00000202e1155100, 0x00000202e3fd1f10,
0x00000202e3fd0b60, 0x00000202e3fcea90, 0x00000202e3fcf120, 0x00000202e3fcd6e0,
0x00000202e3fd1880, 0x00000202e3fd3fe0, 0x00000202e3fcf7b0, 0x00000202e3fd25a0,
0x00000202e3fcdd70, 0x00000202e3fd4d00, 0x00000202e3fce400, 0x00000202e3fd2c30,
0x00000202e3fd32c0, 0x00000202e3fcfe40, 0x00000202e3fd11f0, 0x00000202e3fd04d0,
0x00000202e3fd4670, 0x00000202e3fd3950, 0x00000202e82bde70, 0x00000202e82c05d0,
0x00000202e82c2d30, 0x00000202e11543e0, 0x00000202e5843e90, 0x00000202e5845f60,
0x00000202e5843800, 0x00000202e5fc8c60, 0x00000202e5fcc0e0, 0x00000202e9849690,
0x00000202e98447d0, 0x00000202e98482e0, 0x00000202e9845b80, 0x00000202e98468a0,
0x00000202e9844e60, 0x00000202e984aa40, 0x00000202e9846f30, 0x00000202e9849d20,
0x00000202e98475c0, 0x00000202e984a3b0, 0x00000202e9843ab0, 0x00000202e98454f0,
0x00000202e9848970, 0x00000202e3d9cc10, 0x00000202e1aec2c0, 0x00000202e9849000,
0x00000202e984b0d0, 0x00000202e9844140, 0x00000202e5fca010, 0x00000202e5fc78b0,
0x00000202e5fcce00, 0x00000202e5fce1b0, 0x00000202e5fcba50, 0x00000202e5fcb3c0,
0x00000202e5fcd490, 0x00000202e5fceed0, 0x00000202e5fc92f0, 0x00000202e5fc9980,
0x00000202e5fca6a0, 0x00000202e5845240, 0x00000202e5841730, 0x00000202e58458d0,
0x00000202e5842ae0, 0x00000202e5841dc0, 0x00000202e583f660, 0x00000202e58465f0,
0x00000202e5842450, 0x00000202e583fcf0, 0x00000202e82c0c60, 0x00000202e82bff40,
0x00000202e82c40e0, 0x00000202e9693950, 0x00000202e96904d0, 0x00000202e968ea90,
0x00000202e9691f10, 0x00000202e96960b0, 0x00000202e9693fe0, 0x00000202e968f7b0,
0x00000202e96925a0, 0x00000202e9690b60, 0x00000202e9694670, 0x00000202e9692c30,
0x00000202e9694d00, 0x00000202e9695a20, 0x00000202e9696dd0, 0x00000202e79aed60,
0x00000202e79a7740, 0x00000202e79a7dd0, 0x00000202e79a9810, 0x00000202ee35c600,
0x00000202ee35cc90
}

Java Threads: ( => current thread )
  0x00000202fbe664e0 JavaThread "main"                              [_thread_blocked, id=19256, stack(0x0000008960c00000,0x0000008960d00000) (1024K)]
  0x00000202ff4b61a0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=4148, stack(0x0000008961400000,0x0000008961500000) (1024K)]
  0x00000202ff4b6fd0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=13004, stack(0x0000008961500000,0x0000008961600000) (1024K)]
  0x00000202ff4b9830 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=1360, stack(0x0000008961600000,0x0000008961700000) (1024K)]
  0x00000202ff4bc3a0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=19248, stack(0x0000008961700000,0x0000008961800000) (1024K)]
  0x00000202ff4bce00 JavaThread "Service Thread"             daemon [_thread_blocked, id=3368, stack(0x0000008961800000,0x0000008961900000) (1024K)]
  0x00000202ff4bf870 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=18812, stack(0x0000008961900000,0x0000008961a00000) (1024K)]
=>0x000002029b42a310 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=11928, stack(0x0000008961a00000,0x0000008961b00000) (1024K)]
  0x00000202ff4c57e0 JavaThread "C1 CompilerThread0"         daemon [_thread_in_vm, id=17540, stack(0x0000008961b00000,0x0000008961c00000) (1024K)]
  0x000002029b505670 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=8920, stack(0x0000008961c00000,0x0000008961d00000) (1024K)]
  0x000002029b6d2fa0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=1756, stack(0x0000008961d00000,0x0000008961e00000) (1024K)]
  0x00000202e27b5440 JavaThread "Daemon health stats"               [_thread_blocked, id=19104, stack(0x0000008961f00000,0x0000008962000000) (1024K)]
  0x00000202e1f1cc00 JavaThread "Incoming local TCP Connector on port 50149"        [_thread_in_native, id=13368, stack(0x0000008962700000,0x0000008962800000) (1024K)]
  0x00000202e2a04db0 JavaThread "Daemon periodic checks"            [_thread_blocked, id=13520, stack(0x0000008962800000,0x0000008962900000) (1024K)]
  0x00000202e28c0310 JavaThread "Daemon"                            [_thread_blocked, id=8996, stack(0x0000008962900000,0x0000008962a00000) (1024K)]
  0x00000202e283c520 JavaThread "Handler for socket connection from /127.0.0.1:50149 to /127.0.0.1:50150"        [_thread_in_native, id=10692, stack(0x0000008962a00000,0x0000008962b00000) (1024K)]
  0x00000202e29db2d0 JavaThread "Cancel handler"                    [_thread_blocked, id=268, stack(0x0000008962b00000,0x0000008962c00000) (1024K)]
  0x00000202e28a1750 JavaThread "Daemon worker"                     [_thread_blocked, id=6496, stack(0x0000008962c00000,0x0000008962d00000) (1024K)]
  0x00000202e28a1de0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:50149 to /127.0.0.1:50150"        [_thread_blocked, id=15348, stack(0x0000008962d00000,0x0000008962e00000) (1024K)]
  0x00000202e289fd10 JavaThread "Stdin handler"                     [_thread_blocked, id=17328, stack(0x0000008962e00000,0x0000008962f00000) (1024K)]
  0x00000202e28a10c0 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=7900, stack(0x0000008962f00000,0x0000008963000000) (1024K)]
  0x00000202e28a2470 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=13916, stack(0x0000008963000000,0x0000008963100000) (1024K)]
  0x00000202e28a0a30 JavaThread "File lock request listener"        [_thread_in_native, id=5176, stack(0x0000008963100000,0x0000008963200000) (1024K)]
  0x00000202e28a2b00 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.11.1\fileHashes)"        [_thread_blocked, id=15608, stack(0x0000008963200000,0x0000008963300000) (1024K)]
  0x00000202e28a03a0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.11.1\fileContent)"        [_thread_blocked, id=12716, stack(0x0000008963400000,0x0000008963500000) (1024K)]
  0x00000202e44d7b80 JavaThread "Cache worker for file hash cache (C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\.gradle\8.11.1\fileHashes)"        [_thread_blocked, id=5340, stack(0x0000008963600000,0x0000008963700000) (1024K)]
  0x00000202e44d74f0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\.gradle\buildOutputCleanup)"        [_thread_blocked, id=18512, stack(0x0000008963700000,0x0000008963800000) (1024K)]
  0x00000202e44d8210 JavaThread "File watcher server"        daemon [_thread_in_native, id=6968, stack(0x0000008963800000,0x0000008963900000) (1024K)]
  0x00000202e44dd0d0 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=18400, stack(0x0000008963900000,0x0000008963a00000) (1024K)]
  0x00000202e44da2e0 JavaThread "Cache worker for checksums cache (C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\.gradle\8.11.1\checksums)"        [_thread_blocked, id=8132, stack(0x0000008963a00000,0x0000008963b00000) (1024K)]
  0x00000202e44da970 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.11.1\md-rule)"        [_thread_blocked, id=18672, stack(0x0000008963b00000,0x0000008963c00000) (1024K)]
  0x00000202e44db690 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.11.1\md-supplier)"        [_thread_blocked, id=12540, stack(0x0000008963c00000,0x0000008963d00000) (1024K)]
  0x00000202e44d6e60 JavaThread "jar transforms"                    [_thread_blocked, id=7544, stack(0x0000008963d00000,0x0000008963e00000) (1024K)]
  0x00000202e44dbd20 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=9640, stack(0x0000008963e00000,0x0000008963f00000) (1024K)]
  0x00000202e44d95c0 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=14432, stack(0x0000008963f00000,0x0000008964000000) (1024K)]
  0x00000202e44d9c50 JavaThread "Unconstrained build operations"        [_thread_blocked, id=7696, stack(0x0000008964000000,0x0000008964100000) (1024K)]
  0x00000202e44dc3b0 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=14736, stack(0x0000008964100000,0x0000008964200000) (1024K)]
  0x00000202e44d67d0 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=11188, stack(0x0000008964200000,0x0000008964300000) (1024K)]
  0x00000202e4d63aa0 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=17632, stack(0x0000008964300000,0x0000008964400000) (1024K)]
  0x00000202e4d65b70 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=13668, stack(0x0000008964400000,0x0000008964500000) (1024K)]
  0x00000202e4d68ff0 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=15228, stack(0x0000008964500000,0x0000008964600000) (1024K)]
  0x00000202e4d675b0 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=18840, stack(0x0000008964600000,0x0000008964700000) (1024K)]
  0x00000202e4d66200 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=9032, stack(0x0000008964700000,0x0000008964800000) (1024K)]
  0x00000202e4d64e50 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=4260, stack(0x0000008964800000,0x0000008964900000) (1024K)]
  0x00000202e4d62060 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=2416, stack(0x0000008964900000,0x0000008964a00000) (1024K)]
  0x00000202e4d647c0 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=18456, stack(0x0000008964a00000,0x0000008964b00000) (1024K)]
  0x00000202e4d654e0 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=7304, stack(0x0000008964b00000,0x0000008964c00000) (1024K)]
  0x00000202e4d69680 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=10976, stack(0x0000008964c00000,0x0000008964d00000) (1024K)]
  0x00000202e4d66890 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=296, stack(0x0000008964d00000,0x0000008964e00000) (1024K)]
  0x00000202e4d66f20 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=2696, stack(0x0000008964e00000,0x0000008964f00000) (1024K)]
  0x00000202e4d67c40 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=1912, stack(0x0000008964f00000,0x0000008965000000) (1024K)]
  0x00000202e4d626f0 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=11432, stack(0x0000008965000000,0x0000008965100000) (1024K)]
  0x00000202e4d682d0 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=6504, stack(0x0000008965100000,0x0000008965200000) (1024K)]
  0x00000202e4d62d80 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=2452, stack(0x0000008965200000,0x0000008965300000) (1024K)]
  0x00000202e4d63410 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=13160, stack(0x0000008965300000,0x0000008965400000) (1024K)]
  0x00000202e4d68960 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=9592, stack(0x0000008965400000,0x0000008965500000) (1024K)]
  0x00000202e1153030 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=15868, stack(0x0000008965500000,0x0000008965600000) (1024K)]
  0x00000202e1155e20 JavaThread "jar transforms Thread 5"           [_thread_blocked, id=2572, stack(0x0000008965600000,0x0000008965700000) (1024K)]
  0x00000202e1155100 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=12376, stack(0x0000008965700000,0x0000008965800000) (1024K)]
  0x00000202e3fd1f10 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=11528, stack(0x0000008965800000,0x0000008965900000) (1024K)]
  0x00000202e3fd0b60 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=7920, stack(0x0000008965900000,0x0000008965a00000) (1024K)]
  0x00000202e3fcea90 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=13736, stack(0x0000008965a00000,0x0000008965b00000) (1024K)]
  0x00000202e3fcf120 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=13944, stack(0x0000008965b00000,0x0000008965c00000) (1024K)]
  0x00000202e3fcd6e0 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=428, stack(0x0000008965c00000,0x0000008965d00000) (1024K)]
  0x00000202e3fd1880 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=16920, stack(0x0000008965d00000,0x0000008965e00000) (1024K)]
  0x00000202e3fd3fe0 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=16492, stack(0x0000008965e00000,0x0000008965f00000) (1024K)]
  0x00000202e3fcf7b0 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=9152, stack(0x0000008965f00000,0x0000008966000000) (1024K)]
  0x00000202e3fd25a0 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=11520, stack(0x0000008966000000,0x0000008966100000) (1024K)]
  0x00000202e3fcdd70 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=17404, stack(0x0000008966100000,0x0000008966200000) (1024K)]
  0x00000202e3fd4d00 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=15636, stack(0x0000008966200000,0x0000008966300000) (1024K)]
  0x00000202e3fce400 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=9952, stack(0x0000008966300000,0x0000008966400000) (1024K)]
  0x00000202e3fd2c30 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=17144, stack(0x0000008966400000,0x0000008966500000) (1024K)]
  0x00000202e3fd32c0 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=10612, stack(0x0000008966500000,0x0000008966600000) (1024K)]
  0x00000202e3fcfe40 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=12516, stack(0x0000008966600000,0x0000008966700000) (1024K)]
  0x00000202e3fd11f0 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=13676, stack(0x0000008966700000,0x0000008966800000) (1024K)]
  0x00000202e3fd04d0 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=15952, stack(0x0000008966800000,0x0000008966900000) (1024K)]
  0x00000202e3fd4670 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=13128, stack(0x0000008966900000,0x0000008966a00000) (1024K)]
  0x00000202e3fd3950 JavaThread "Unconstrained build operations Thread 41"        [_thread_blocked, id=8956, stack(0x0000008966a00000,0x0000008966b00000) (1024K)]
  0x00000202e82bde70 JavaThread "Unconstrained build operations Thread 42"        [_thread_blocked, id=15776, stack(0x0000008966b00000,0x0000008966c00000) (1024K)]
  0x00000202e82c05d0 JavaThread "Memory manager"                    [_thread_blocked, id=16624, stack(0x0000008963300000,0x0000008963400000) (1024K)]
  0x00000202e82c2d30 JavaThread "build event listener"              [_thread_blocked, id=13576, stack(0x0000008966c00000,0x0000008966d00000) (1024K)]
  0x00000202e11543e0 JavaThread "jar transforms Thread 6"           [_thread_blocked, id=19272, stack(0x0000008966d00000,0x0000008966e00000) (1024K)]
  0x00000202e5843e90 JavaThread "build event listener"              [_thread_blocked, id=13972, stack(0x0000008966f00000,0x0000008967000000) (1024K)]
  0x00000202e5845f60 JavaThread "jar transforms Thread 7"           [_thread_blocked, id=4784, stack(0x0000008966e00000,0x0000008966f00000) (1024K)]
  0x00000202e5843800 JavaThread "jar transforms Thread 8"           [_thread_blocked, id=5568, stack(0x0000008967000000,0x0000008967100000) (1024K)]
  0x00000202e5fc8c60 JavaThread "pool-3-thread-1"                   [_thread_blocked, id=8588, stack(0x0000008967200000,0x0000008967300000) (1024K)]
  0x00000202e5fcc0e0 JavaThread "build event listener"              [_thread_blocked, id=6044, stack(0x0000008967300000,0x0000008967400000) (1024K)]
  0x00000202e9849690 JavaThread "Exec process"                      [_thread_blocked, id=2192, stack(0x0000008961e00000,0x0000008961f00000) (1024K)]
  0x00000202e98447d0 JavaThread "Exec process Thread 2"             [_thread_blocked, id=13180, stack(0x0000008967100000,0x0000008967200000) (1024K)]
  0x00000202e98482e0 JavaThread "Exec process Thread 3"             [_thread_blocked, id=8020, stack(0x0000008967400000,0x0000008967500000) (1024K)]
  0x00000202e9845b80 JavaThread "SentryExecutorServiceThreadFactory-0" daemon [_thread_blocked, id=8844, stack(0x0000008967500000,0x0000008967600000) (1024K)]
  0x00000202e98468a0 JavaThread "SentryAsyncConnection-0"    daemon [_thread_blocked, id=13732, stack(0x0000008967600000,0x0000008967700000) (1024K)]
  0x00000202e9844e60 JavaThread "build event listener"              [_thread_blocked, id=17132, stack(0x0000008967700000,0x0000008967800000) (1024K)]
  0x00000202e984aa40 JavaThread "included builds"                   [_thread_in_vm, id=17940, stack(0x0000008967800000,0x0000008967900000) (1024K)]
  0x00000202e9846f30 JavaThread "Execution worker"                  [_thread_blocked, id=7964, stack(0x0000008967900000,0x0000008967a00000) (1024K)]
  0x00000202e9849d20 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=1532, stack(0x0000008967a00000,0x0000008967b00000) (1024K)]
  0x00000202e98475c0 JavaThread "Execution worker Thread 3"         [_thread_in_vm, id=2856, stack(0x0000008967b00000,0x0000008967c00000) (1024K)]
  0x00000202e984a3b0 JavaThread "Execution worker Thread 4"         [_thread_in_vm, id=9496, stack(0x0000008967c00000,0x0000008967d00000) (1024K)]
  0x00000202e9843ab0 JavaThread "Execution worker Thread 5"         [_thread_blocked, id=17644, stack(0x0000008967d00000,0x0000008967e00000) (1024K)]
  0x00000202e98454f0 JavaThread "Execution worker Thread 6"         [_thread_blocked, id=1204, stack(0x0000008967e00000,0x0000008967f00000) (1024K)]
  0x00000202e9848970 JavaThread "Execution worker Thread 7"         [_thread_blocked, id=7204, stack(0x0000008967f00000,0x0000008968000000) (1024K)]
  0x00000202e3d9cc10 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=12384, stack(0x0000008968000000,0x0000008968100000) (1024K)]
  0x00000202e1aec2c0 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=17776, stack(0x0000008968100000,0x0000008968200000) (1024K)]
  0x00000202e9849000 JavaThread "Unconstrained build operations Thread 43"        [_thread_blocked, id=12160, stack(0x0000008968300000,0x0000008968400000) (1024K)]
  0x00000202e984b0d0 JavaThread "Unconstrained build operations Thread 44"        [_thread_blocked, id=2132, stack(0x0000008968400000,0x0000008968500000) (1024K)]
  0x00000202e9844140 JavaThread "Unconstrained build operations Thread 45"        [_thread_blocked, id=15728, stack(0x0000008968500000,0x0000008968600000) (1024K)]
  0x00000202e5fca010 JavaThread "Unconstrained build operations Thread 46"        [_thread_blocked, id=16236, stack(0x0000008968600000,0x0000008968700000) (1024K)]
  0x00000202e5fc78b0 JavaThread "Unconstrained build operations Thread 47"        [_thread_blocked, id=17308, stack(0x0000008968700000,0x0000008968800000) (1024K)]
  0x00000202e5fcce00 JavaThread "Unconstrained build operations Thread 48"        [_thread_blocked, id=4140, stack(0x0000008968800000,0x0000008968900000) (1024K)]
  0x00000202e5fce1b0 JavaThread "Unconstrained build operations Thread 49"        [_thread_blocked, id=1344, stack(0x0000008968900000,0x0000008968a00000) (1024K)]
  0x00000202e5fcba50 JavaThread "Unconstrained build operations Thread 50"        [_thread_blocked, id=12508, stack(0x0000008968a00000,0x0000008968b00000) (1024K)]
  0x00000202e5fcb3c0 JavaThread "Unconstrained build operations Thread 51"        [_thread_blocked, id=3560, stack(0x0000008968b00000,0x0000008968c00000) (1024K)]
  0x00000202e5fcd490 JavaThread "Unconstrained build operations Thread 52"        [_thread_blocked, id=14876, stack(0x0000008968c00000,0x0000008968d00000) (1024K)]
  0x00000202e5fceed0 JavaThread "Unconstrained build operations Thread 53"        [_thread_blocked, id=7420, stack(0x0000008968d00000,0x0000008968e00000) (1024K)]
  0x00000202e5fc92f0 JavaThread "Unconstrained build operations Thread 54"        [_thread_blocked, id=13584, stack(0x0000008968e00000,0x0000008968f00000) (1024K)]
  0x00000202e5fc9980 JavaThread "Unconstrained build operations Thread 55"        [_thread_blocked, id=4200, stack(0x0000008968f00000,0x0000008969000000) (1024K)]
  0x00000202e5fca6a0 JavaThread "Unconstrained build operations Thread 56"        [_thread_blocked, id=18248, stack(0x0000008969000000,0x0000008969100000) (1024K)]
  0x00000202e5845240 JavaThread "Unconstrained build operations Thread 57"        [_thread_blocked, id=19304, stack(0x0000008969100000,0x0000008969200000) (1024K)]
  0x00000202e5841730 JavaThread "Unconstrained build operations Thread 58"        [_thread_blocked, id=16756, stack(0x0000008969200000,0x0000008969300000) (1024K)]
  0x00000202e58458d0 JavaThread "Unconstrained build operations Thread 59"        [_thread_blocked, id=19412, stack(0x0000008969300000,0x0000008969400000) (1024K)]
  0x00000202e5842ae0 JavaThread "Unconstrained build operations Thread 60"        [_thread_blocked, id=10724, stack(0x0000008969400000,0x0000008969500000) (1024K)]
  0x00000202e5841dc0 JavaThread "Cache worker for execution history cache (C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\.gradle\8.11.1\executionHistory)"        [_thread_blocked, id=11848, stack(0x0000008969500000,0x0000008969600000) (1024K)]
  0x00000202e583f660 JavaThread "Unconstrained build operations Thread 61"        [_thread_blocked, id=18832, stack(0x0000008969600000,0x0000008969700000) (1024K)]
  0x00000202e58465f0 JavaThread "Unconstrained build operations Thread 62"        [_thread_blocked, id=4848, stack(0x0000008969700000,0x0000008969800000) (1024K)]
  0x00000202e5842450 JavaThread "Unconstrained build operations Thread 63"        [_thread_blocked, id=4516, stack(0x0000008969800000,0x0000008969900000) (1024K)]
  0x00000202e583fcf0 JavaThread "Unconstrained build operations Thread 64"        [_thread_blocked, id=12836, stack(0x0000008969900000,0x0000008969a00000) (1024K)]
  0x00000202e82c0c60 JavaThread "Unconstrained build operations Thread 65"        [_thread_blocked, id=18284, stack(0x0000008969a00000,0x0000008969b00000) (1024K)]
  0x00000202e82bff40 JavaThread "Unconstrained build operations Thread 66"        [_thread_blocked, id=9604, stack(0x0000008969b00000,0x0000008969c00000) (1024K)]
  0x00000202e82c40e0 JavaThread "Unconstrained build operations Thread 67"        [_thread_blocked, id=16184, stack(0x0000008969c00000,0x0000008969d00000) (1024K)]
  0x00000202e9693950 JavaThread "Unconstrained build operations Thread 68"        [_thread_blocked, id=9576, stack(0x0000008969d00000,0x0000008969e00000) (1024K)]
  0x00000202e96904d0 JavaThread "Unconstrained build operations Thread 69"        [_thread_blocked, id=14116, stack(0x0000008969e00000,0x0000008969f00000) (1024K)]
  0x00000202e968ea90 JavaThread "Unconstrained build operations Thread 70"        [_thread_blocked, id=1816, stack(0x0000008969f00000,0x000000896a000000) (1024K)]
  0x00000202e9691f10 JavaThread "Unconstrained build operations Thread 71"        [_thread_blocked, id=16856, stack(0x000000896a000000,0x000000896a100000) (1024K)]
  0x00000202e96960b0 JavaThread "Unconstrained build operations Thread 72"        [_thread_blocked, id=14884, stack(0x000000896a100000,0x000000896a200000) (1024K)]
  0x00000202e9693fe0 JavaThread "Unconstrained build operations Thread 73"        [_thread_blocked, id=7844, stack(0x000000896a200000,0x000000896a300000) (1024K)]
  0x00000202e968f7b0 JavaThread "Unconstrained build operations Thread 74"        [_thread_blocked, id=10280, stack(0x000000896a300000,0x000000896a400000) (1024K)]
  0x00000202e96925a0 JavaThread "Unconstrained build operations Thread 75"        [_thread_blocked, id=12396, stack(0x000000896a400000,0x000000896a500000) (1024K)]
  0x00000202e9690b60 JavaThread "Unconstrained build operations Thread 76"        [_thread_blocked, id=1544, stack(0x000000896a500000,0x000000896a600000) (1024K)]
  0x00000202e9694670 JavaThread "Unconstrained build operations Thread 77"        [_thread_blocked, id=18656, stack(0x000000896a600000,0x000000896a700000) (1024K)]
  0x00000202e9692c30 JavaThread "Unconstrained build operations Thread 78"        [_thread_blocked, id=2068, stack(0x000000896a700000,0x000000896a800000) (1024K)]
  0x00000202e9694d00 JavaThread "Unconstrained build operations Thread 79"        [_thread_blocked, id=16812, stack(0x000000896a800000,0x000000896a900000) (1024K)]
  0x00000202e9695a20 JavaThread "Unconstrained build operations Thread 80"        [_thread_blocked, id=19384, stack(0x000000896a900000,0x000000896aa00000) (1024K)]
  0x00000202e9696dd0 JavaThread "WorkerExecutor Queue"              [_thread_blocked, id=9024, stack(0x000000896aa00000,0x000000896ab00000) (1024K)]
  0x00000202e79aed60 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_blocked, id=10556, stack(0x000000896ab00000,0x000000896ac00000) (1024K)]
  0x00000202e79a7740 JavaThread "pool-4-thread-1"                   [_thread_blocked, id=12104, stack(0x000000896ac00000,0x000000896ad00000) (1024K)]
  0x00000202e79a7dd0 JavaThread "stderr"                            [_thread_in_native, id=300, stack(0x000000896ad00000,0x000000896ae00000) (1024K)]
  0x00000202e79a9810 JavaThread "stdout"                            [_thread_in_native, id=11688, stack(0x000000896ae00000,0x000000896af00000) (1024K)]
  0x00000202ee35c600 JavaThread "WorkerExecutor Queue Thread 3"        [_thread_blocked, id=17096, stack(0x000000896af00000,0x000000896b000000) (1024K)]
  0x00000202ee35cc90 JavaThread "WorkerExecutor Queue Thread 4"        [_thread_blocked, id=10236, stack(0x000000896b000000,0x000000896b100000) (1024K)]
Total: 149

Other Threads:
  0x00000202ff4991e0 VMThread "VM Thread"                           [id=13968, stack(0x0000008961300000,0x0000008961400000) (1024K)]
  0x000002029b4209d0 WatcherThread "VM Periodic Task Thread"        [id=10952, stack(0x0000008961200000,0x0000008961300000) (1024K)]
  0x00000202fbebfe50 WorkerThread "GC Thread#0"                     [id=6972, stack(0x0000008960d00000,0x0000008960e00000) (1024K)]
  0x00000202e130f2c0 WorkerThread "GC Thread#1"                     [id=16072, stack(0x0000008962000000,0x0000008962100000) (1024K)]
  0x00000202e15905c0 WorkerThread "GC Thread#2"                     [id=11512, stack(0x0000008962100000,0x0000008962200000) (1024K)]
  0x00000202e1590960 WorkerThread "GC Thread#3"                     [id=7636, stack(0x0000008962200000,0x0000008962300000) (1024K)]
  0x00000202e13c0cf0 WorkerThread "GC Thread#4"                     [id=12164, stack(0x0000008962300000,0x0000008962400000) (1024K)]
  0x00000202e13c1090 WorkerThread "GC Thread#5"                     [id=1868, stack(0x0000008962400000,0x0000008962500000) (1024K)]
  0x00000202e13c1430 WorkerThread "GC Thread#6"                     [id=9260, stack(0x0000008962500000,0x0000008962600000) (1024K)]
  0x000002029b705720 WorkerThread "GC Thread#7"                     [id=9676, stack(0x0000008962600000,0x0000008962700000) (1024K)]
  0x00000202fbed0ef0 ConcurrentGCThread "G1 Main Marker"            [id=9208, stack(0x0000008960e00000,0x0000008960f00000) (1024K)]
  0x00000202fbed20a0 WorkerThread "G1 Conc#0"                       [id=16740, stack(0x0000008960f00000,0x0000008961000000) (1024K)]
  0x00000202e2916320 WorkerThread "G1 Conc#1"                       [id=18264, stack(0x0000008963500000,0x0000008963600000) (1024K)]
  0x00000202ff3d3690 ConcurrentGCThread "G1 Refine#0"               [id=19432, stack(0x0000008961000000,0x0000008961100000) (1024K)]
  0x00000202fbf2e5b0 ConcurrentGCThread "G1 Service"                [id=17576, stack(0x0000008961100000,0x0000008961200000) (1024K)]
Total: 15

Threads with active compile tasks:
C2 CompilerThread0    26467 17595       4       org.gradle.api.internal.artifacts.transform.AbstractTransformExecution::identify (14 bytes)
C1 CompilerThread0    26467 17765       2       java.lang.StackTraceElement::length (13 bytes)
C2 CompilerThread1    26467 17659       4       org.gradle.internal.execution.impl.DefaultInputFingerprinter::fingerprintInputProperties (41 bytes)
C2 CompilerThread2    26467 17621       4       org.gradle.api.internal.artifacts.transform.TransformingAsyncArtifactListener$TransformedArtifact::lambda$visit$3 (65 bytes)
Total: 4

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fff7e33ace8] Metaspace_lock - owner thread: 0x00000202ff4c57e0

OutOfMemory and StackOverflow Exception counts:
OutOfMemoryError java_heap_errors=1
LinkageErrors=236

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002029c000000-0x000002029cc90000-0x000002029cc90000), size 13172736, SharedBaseAddress: 0x000002029c000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002029d000000-0x00000202dd000000, reserved size: 1073741824
Narrow klass base: 0x000002029c000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 8 total, 8 available
 Memory: 22476M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 352M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 301056K, used 263453K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 103 young (105472K), 10 survivors (10240K)
 Metaspace       used 120369K, committed 122752K, reserved 1179648K
  class space    used 16335K, committed 17536K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%| O|  |TAMS 0x0000000080000000| PB 0x0000000080000000| Untracked 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HS|  |TAMS 0x0000000080100000| PB 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080200000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080300000| PB 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080400000| PB 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080500000| PB 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080600000| PB 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%|HS|  |TAMS 0x0000000080700000| PB 0x0000000080700000| Complete 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080800000| PB 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080900000| PB 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080a00000| PB 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080b00000| PB 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080c00000| PB 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080d00000| PB 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080e00000| PB 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000080f00000| PB 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081000000| PB 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081100000| PB 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081200000| PB 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081300000| PB 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081400000| PB 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081500000| PB 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081600000| PB 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081700000| PB 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081800000| PB 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081900000| PB 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081a00000| PB 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081b00000| PB 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081c00000| PB 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081d00000| PB 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081e00000| PB 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000081f00000| PB 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082000000| PB 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082100000| PB 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082200000| PB 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082300000| PB 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082400000| PB 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082500000| PB 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082600000| PB 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%|HS|  |TAMS 0x0000000082700000| PB 0x0000000082700000| Complete 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%|HS|  |TAMS 0x0000000082800000| PB 0x0000000082800000| Complete 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082900000| PB 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082a00000| PB 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082b00000| PB 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082c00000| PB 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%| O|  |TAMS 0x0000000082d00000| PB 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%|HS|  |TAMS 0x0000000082e00000| PB 0x0000000082e00000| Complete 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%| O|  |TAMS 0x0000000082f00000| PB 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%| O|  |TAMS 0x0000000083000000| PB 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%| O|  |TAMS 0x0000000083100000| PB 0x0000000083100000| Untracked 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%|HS|  |TAMS 0x0000000083200000| PB 0x0000000083200000| Complete 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%|HC|  |TAMS 0x0000000083300000| PB 0x0000000083300000| Complete 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%| O|  |TAMS 0x0000000083400000| PB 0x0000000083400000| Untracked 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%| O|  |TAMS 0x0000000083500000| PB 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%|HS|  |TAMS 0x0000000083600000| PB 0x0000000083600000| Complete 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%|HC|  |TAMS 0x0000000083700000| PB 0x0000000083700000| Complete 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%| O|  |TAMS 0x0000000083800000| PB 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%| O|  |TAMS 0x0000000083900000| PB 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%| O|  |TAMS 0x0000000083a00000| PB 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%| O|  |TAMS 0x0000000083b00000| PB 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%| O|  |TAMS 0x0000000083c00000| PB 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%|HS|  |TAMS 0x0000000083d00000| PB 0x0000000083d00000| Complete 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%|HC|  |TAMS 0x0000000083e00000| PB 0x0000000083e00000| Complete 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%|HC|  |TAMS 0x0000000083f00000| PB 0x0000000083f00000| Complete 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%|HS|  |TAMS 0x0000000084000000| PB 0x0000000084000000| Complete 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%|HS|  |TAMS 0x0000000084100000| PB 0x0000000084100000| Complete 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%|HS|  |TAMS 0x0000000084200000| PB 0x0000000084200000| Complete 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|Cm|TAMS 0x0000000084300000| PB 0x0000000084300000| Complete 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084400000| PB 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084500000| PB 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084600000| PB 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084700000| PB 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084800000| PB 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%| O|  |TAMS 0x0000000084900000| PB 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|Cm|TAMS 0x0000000084a00000| PB 0x0000000084a00000| Complete 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|Cm|TAMS 0x0000000084b00000| PB 0x0000000084b00000| Complete 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084c00000| PB 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084d00000| PB 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084e00000| PB 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%|HS|  |TAMS 0x0000000084f00000| PB 0x0000000084f00000| Complete 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085000000| PB 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085100000| PB 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085200000| PB 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085300000| PB 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085400000| PB 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%|HS|  |TAMS 0x0000000085500000| PB 0x0000000085500000| Complete 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%|HC|  |TAMS 0x0000000085600000| PB 0x0000000085600000| Complete 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%|HC|  |TAMS 0x0000000085700000| PB 0x0000000085700000| Complete 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|Cm|TAMS 0x0000000085800000| PB 0x0000000085800000| Complete 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085900000| PB 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085a00000| PB 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085b00000| PB 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085c00000| PB 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085d00000| PB 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085e00000| PB 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000085f00000| PB 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086000000| PB 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086100000| PB 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|Cm|TAMS 0x0000000086200000| PB 0x0000000086200000| Complete 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086300000| PB 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086400000| PB 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086500000| PB 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086600000| PB 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086700000| PB 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086800000| PB 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086900000| PB 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086a00000| PB 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086b00000| PB 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086c00000| PB 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086d00000| PB 0x0000000086d00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086e00000| PB 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000086f00000| PB 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087000000| PB 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087100000| PB 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087200000| PB 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| O|  |TAMS 0x0000000087300000| PB 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%| O|  |TAMS 0x0000000087400000| PB 0x0000000087400000| Untracked 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087500000| PB 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087600000| PB 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087700000| PB 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| O|  |TAMS 0x0000000087800000| PB 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087900000| PB 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| O|  |TAMS 0x0000000087a00000| PB 0x0000000087a00000| Untracked 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| O|  |TAMS 0x0000000087b00000| PB 0x0000000087b00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| O|  |TAMS 0x0000000087c00000| PB 0x0000000087c00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| O|  |TAMS 0x0000000087d00000| PB 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087e00000| PB 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000087f47550, 0x0000000088000000| 27%| O|  |TAMS 0x0000000087f00000| PB 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%| O|  |TAMS 0x0000000088000000| PB 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| O|  |TAMS 0x0000000088100000| PB 0x0000000088100000| Untracked 
| 130|0x0000000088200000, 0x0000000088200000, 0x0000000088300000|  0%| F|  |TAMS 0x0000000088200000| PB 0x0000000088200000| Untracked 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| O|  |TAMS 0x0000000088300000| PB 0x0000000088300000| Untracked 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%| O|  |TAMS 0x0000000088400000| PB 0x0000000088400000| Untracked 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%| O|  |TAMS 0x0000000088500000| PB 0x0000000088500000| Untracked 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%| O|  |TAMS 0x0000000088600000| PB 0x0000000088600000| Untracked 
| 135|0x0000000088700000, 0x0000000088700000, 0x0000000088800000|  0%| F|  |TAMS 0x0000000088700000| PB 0x0000000088700000| Untracked 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%| O|  |TAMS 0x0000000088800000| PB 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| O|  |TAMS 0x0000000088900000| PB 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088a00000| PB 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|  |TAMS 0x0000000088b00000| PB 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| O|  |TAMS 0x0000000088c00000| PB 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%| O|  |TAMS 0x0000000088d00000| PB 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%| O|  |TAMS 0x0000000088e00000| PB 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%| O|  |TAMS 0x0000000088f00000| PB 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%| O|  |TAMS 0x0000000089000000| PB 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%| O|  |TAMS 0x0000000089100000| PB 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%| O|  |TAMS 0x0000000089200000| PB 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%| O|  |TAMS 0x0000000089300000| PB 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%| O|Cm|TAMS 0x0000000089400000| PB 0x0000000089400000| Complete 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%|HS|  |TAMS 0x0000000089500000| PB 0x0000000089500000| Complete 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%|HC|  |TAMS 0x0000000089600000| PB 0x0000000089600000| Complete 
| 151|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%|HS|  |TAMS 0x0000000089700000| PB 0x0000000089700000| Complete 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%|HC|  |TAMS 0x0000000089800000| PB 0x0000000089800000| Complete 
| 153|0x0000000089900000, 0x0000000089900000, 0x0000000089a00000|  0%| F|  |TAMS 0x0000000089900000| PB 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089a00000, 0x0000000089b00000|  0%| F|  |TAMS 0x0000000089a00000| PB 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089b00000, 0x0000000089c00000|  0%| F|  |TAMS 0x0000000089b00000| PB 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089c00000, 0x0000000089d00000|  0%| F|  |TAMS 0x0000000089c00000| PB 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089d00000, 0x0000000089e00000|  0%| F|  |TAMS 0x0000000089d00000| PB 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089e00000, 0x0000000089f00000|  0%| F|  |TAMS 0x0000000089e00000| PB 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x0000000089f00000, 0x000000008a000000|  0%| F|  |TAMS 0x0000000089f00000| PB 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a000000, 0x000000008a100000|  0%| F|  |TAMS 0x000000008a000000| PB 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a100000, 0x000000008a200000|  0%| F|  |TAMS 0x000000008a100000| PB 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a200000, 0x000000008a300000|  0%| F|  |TAMS 0x000000008a200000| PB 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a300000, 0x000000008a400000|  0%| F|  |TAMS 0x000000008a300000| PB 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a400000, 0x000000008a500000|  0%| F|  |TAMS 0x000000008a400000| PB 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a500000, 0x000000008a600000|  0%| F|  |TAMS 0x000000008a500000| PB 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a600000, 0x000000008a700000|  0%| F|  |TAMS 0x000000008a600000| PB 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a700000, 0x000000008a800000|  0%| F|  |TAMS 0x000000008a700000| PB 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a800000, 0x000000008a900000|  0%| F|  |TAMS 0x000000008a800000| PB 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008a900000, 0x000000008aa00000|  0%| F|  |TAMS 0x000000008a900000| PB 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008aa00000, 0x000000008ab00000|  0%| F|  |TAMS 0x000000008aa00000| PB 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%| S|CS|TAMS 0x000000008ab00000| PB 0x000000008ab00000| Complete 
| 172|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%| S|CS|TAMS 0x000000008ac00000| PB 0x000000008ac00000| Complete 
| 173|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%| S|CS|TAMS 0x000000008ad00000| PB 0x000000008ad00000| Complete 
| 174|0x000000008ae00000, 0x000000008af00000, 0x000000008af00000|100%| S|CS|TAMS 0x000000008ae00000| PB 0x000000008ae00000| Complete 
| 175|0x000000008af00000, 0x000000008b000000, 0x000000008b000000|100%| S|CS|TAMS 0x000000008af00000| PB 0x000000008af00000| Complete 
| 176|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%| S|CS|TAMS 0x000000008b000000| PB 0x000000008b000000| Complete 
| 177|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%| S|CS|TAMS 0x000000008b100000| PB 0x000000008b100000| Complete 
| 178|0x000000008b200000, 0x000000008b300000, 0x000000008b300000|100%| S|CS|TAMS 0x000000008b200000| PB 0x000000008b200000| Complete 
| 179|0x000000008b300000, 0x000000008b400000, 0x000000008b400000|100%| S|CS|TAMS 0x000000008b300000| PB 0x000000008b300000| Complete 
| 180|0x000000008b400000, 0x000000008b500000, 0x000000008b500000|100%| S|CS|TAMS 0x000000008b400000| PB 0x000000008b400000| Complete 
| 181|0x000000008b500000, 0x000000008b500000, 0x000000008b600000|  0%| F|  |TAMS 0x000000008b500000| PB 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b600000, 0x000000008b700000|  0%| F|  |TAMS 0x000000008b600000| PB 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b700000, 0x000000008b800000|  0%| F|  |TAMS 0x000000008b700000| PB 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b800000, 0x000000008b900000|  0%| F|  |TAMS 0x000000008b800000| PB 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008b900000, 0x000000008ba00000|  0%| F|  |TAMS 0x000000008b900000| PB 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008ba00000, 0x000000008bb00000|  0%| F|  |TAMS 0x000000008ba00000| PB 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bb00000, 0x000000008bc00000|  0%| F|  |TAMS 0x000000008bb00000| PB 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bc00000, 0x000000008bd00000|  0%| F|  |TAMS 0x000000008bc00000| PB 0x000000008bc00000| Untracked 
| 189|0x000000008bd00000, 0x000000008bd00000, 0x000000008be00000|  0%| F|  |TAMS 0x000000008bd00000| PB 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008be00000, 0x000000008bf00000|  0%| F|  |TAMS 0x000000008be00000| PB 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008bf00000, 0x000000008c000000|  0%| F|  |TAMS 0x000000008bf00000| PB 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c000000, 0x000000008c100000|  0%| F|  |TAMS 0x000000008c000000| PB 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c100000, 0x000000008c200000|  0%| F|  |TAMS 0x000000008c100000| PB 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c200000, 0x000000008c300000|  0%| F|  |TAMS 0x000000008c200000| PB 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c3c0148, 0x000000008c400000| 75%| E|  |TAMS 0x000000008c300000| PB 0x000000008c300000| Complete 
| 196|0x000000008c400000, 0x000000008c500000, 0x000000008c500000|100%| E|CS|TAMS 0x000000008c400000| PB 0x000000008c400000| Complete 
| 197|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%| E|CS|TAMS 0x000000008c500000| PB 0x000000008c500000| Complete 
| 198|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%| E|CS|TAMS 0x000000008c600000| PB 0x000000008c600000| Complete 
| 199|0x000000008c700000, 0x000000008c800000, 0x000000008c800000|100%| E|  |TAMS 0x000000008c700000| PB 0x000000008c700000| Complete 
| 200|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%| E|CS|TAMS 0x000000008c800000| PB 0x000000008c800000| Complete 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%| E|CS|TAMS 0x000000008c900000| PB 0x000000008c900000| Complete 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%| E|CS|TAMS 0x000000008ca00000| PB 0x000000008ca00000| Complete 
| 203|0x000000008cb00000, 0x000000008cc00000, 0x000000008cc00000|100%| E|CS|TAMS 0x000000008cb00000| PB 0x000000008cb00000| Complete 
| 204|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%| E|CS|TAMS 0x000000008cc00000| PB 0x000000008cc00000| Complete 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%| E|CS|TAMS 0x000000008cd00000| PB 0x000000008cd00000| Complete 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%| E|CS|TAMS 0x000000008ce00000| PB 0x000000008ce00000| Complete 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%| E|CS|TAMS 0x000000008cf00000| PB 0x000000008cf00000| Complete 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| E|CS|TAMS 0x000000008d000000| PB 0x000000008d000000| Complete 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| E|CS|TAMS 0x000000008d100000| PB 0x000000008d100000| Complete 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| E|CS|TAMS 0x000000008d200000| PB 0x000000008d200000| Complete 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| E|CS|TAMS 0x000000008d300000| PB 0x000000008d300000| Complete 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| E|CS|TAMS 0x000000008d400000| PB 0x000000008d400000| Complete 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| E|CS|TAMS 0x000000008d500000| PB 0x000000008d500000| Complete 
| 214|0x000000008d600000, 0x000000008d700000, 0x000000008d700000|100%| E|CS|TAMS 0x000000008d600000| PB 0x000000008d600000| Complete 
| 215|0x000000008d700000, 0x000000008d800000, 0x000000008d800000|100%| E|CS|TAMS 0x000000008d700000| PB 0x000000008d700000| Complete 
| 216|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%| E|CS|TAMS 0x000000008d800000| PB 0x000000008d800000| Complete 
| 217|0x000000008d900000, 0x000000008da00000, 0x000000008da00000|100%| E|CS|TAMS 0x000000008d900000| PB 0x000000008d900000| Complete 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| E|CS|TAMS 0x000000008da00000| PB 0x000000008da00000| Complete 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| E|CS|TAMS 0x000000008db00000| PB 0x000000008db00000| Complete 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| E|CS|TAMS 0x000000008dc00000| PB 0x000000008dc00000| Complete 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| E|CS|TAMS 0x000000008dd00000| PB 0x000000008dd00000| Complete 
| 222|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%| E|CS|TAMS 0x000000008de00000| PB 0x000000008de00000| Complete 
| 223|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%| E|CS|TAMS 0x000000008df00000| PB 0x000000008df00000| Complete 
| 224|0x000000008e000000, 0x000000008e100000, 0x000000008e100000|100%| E|CS|TAMS 0x000000008e000000| PB 0x000000008e000000| Complete 
| 225|0x000000008e100000, 0x000000008e200000, 0x000000008e200000|100%| E|CS|TAMS 0x000000008e100000| PB 0x000000008e100000| Complete 
| 226|0x000000008e200000, 0x000000008e300000, 0x000000008e300000|100%| E|CS|TAMS 0x000000008e200000| PB 0x000000008e200000| Complete 
| 227|0x000000008e300000, 0x000000008e400000, 0x000000008e400000|100%| E|CS|TAMS 0x000000008e300000| PB 0x000000008e300000| Complete 
| 228|0x000000008e400000, 0x000000008e500000, 0x000000008e500000|100%| E|CS|TAMS 0x000000008e400000| PB 0x000000008e400000| Complete 
| 229|0x000000008e500000, 0x000000008e600000, 0x000000008e600000|100%| E|CS|TAMS 0x000000008e500000| PB 0x000000008e500000| Complete 
| 230|0x000000008e600000, 0x000000008e700000, 0x000000008e700000|100%| E|CS|TAMS 0x000000008e600000| PB 0x000000008e600000| Complete 
| 231|0x000000008e700000, 0x000000008e800000, 0x000000008e800000|100%| E|CS|TAMS 0x000000008e700000| PB 0x000000008e700000| Complete 
| 232|0x000000008e800000, 0x000000008e900000, 0x000000008e900000|100%| E|CS|TAMS 0x000000008e800000| PB 0x000000008e800000| Complete 
| 233|0x000000008e900000, 0x000000008ea00000, 0x000000008ea00000|100%| E|CS|TAMS 0x000000008e900000| PB 0x000000008e900000| Complete 
| 234|0x000000008ea00000, 0x000000008eb00000, 0x000000008eb00000|100%| E|CS|TAMS 0x000000008ea00000| PB 0x000000008ea00000| Complete 
| 235|0x000000008eb00000, 0x000000008ec00000, 0x000000008ec00000|100%| E|CS|TAMS 0x000000008eb00000| PB 0x000000008eb00000| Complete 
| 236|0x000000008ec00000, 0x000000008ed00000, 0x000000008ed00000|100%| E|CS|TAMS 0x000000008ec00000| PB 0x000000008ec00000| Complete 
| 237|0x000000008ed00000, 0x000000008ee00000, 0x000000008ee00000|100%| E|CS|TAMS 0x000000008ed00000| PB 0x000000008ed00000| Complete 
| 238|0x000000008ee00000, 0x000000008ef00000, 0x000000008ef00000|100%| E|CS|TAMS 0x000000008ee00000| PB 0x000000008ee00000| Complete 
| 239|0x000000008ef00000, 0x000000008f000000, 0x000000008f000000|100%| E|CS|TAMS 0x000000008ef00000| PB 0x000000008ef00000| Complete 
| 240|0x000000008f000000, 0x000000008f100000, 0x000000008f100000|100%| E|CS|TAMS 0x000000008f000000| PB 0x000000008f000000| Complete 
| 241|0x000000008f100000, 0x000000008f200000, 0x000000008f200000|100%| E|CS|TAMS 0x000000008f100000| PB 0x000000008f100000| Complete 
| 242|0x000000008f200000, 0x000000008f300000, 0x000000008f300000|100%| E|CS|TAMS 0x000000008f200000| PB 0x000000008f200000| Complete 
| 243|0x000000008f300000, 0x000000008f400000, 0x000000008f400000|100%| E|CS|TAMS 0x000000008f300000| PB 0x000000008f300000| Complete 
| 244|0x000000008f400000, 0x000000008f500000, 0x000000008f500000|100%| E|CS|TAMS 0x000000008f400000| PB 0x000000008f400000| Complete 
| 245|0x000000008f500000, 0x000000008f600000, 0x000000008f600000|100%| E|CS|TAMS 0x000000008f500000| PB 0x000000008f500000| Complete 
| 246|0x000000008f600000, 0x000000008f700000, 0x000000008f700000|100%| E|CS|TAMS 0x000000008f600000| PB 0x000000008f600000| Complete 
| 247|0x000000008f700000, 0x000000008f800000, 0x000000008f800000|100%| E|CS|TAMS 0x000000008f700000| PB 0x000000008f700000| Complete 
| 248|0x000000008f800000, 0x000000008f900000, 0x000000008f900000|100%| E|CS|TAMS 0x000000008f800000| PB 0x000000008f800000| Complete 
| 249|0x000000008f900000, 0x000000008fa00000, 0x000000008fa00000|100%| E|CS|TAMS 0x000000008f900000| PB 0x000000008f900000| Complete 
| 250|0x000000008fa00000, 0x000000008fb00000, 0x000000008fb00000|100%| E|CS|TAMS 0x000000008fa00000| PB 0x000000008fa00000| Complete 
| 251|0x000000008fb00000, 0x000000008fc00000, 0x000000008fc00000|100%| E|CS|TAMS 0x000000008fb00000| PB 0x000000008fb00000| Complete 
| 252|0x000000008fc00000, 0x000000008fd00000, 0x000000008fd00000|100%| E|CS|TAMS 0x000000008fc00000| PB 0x000000008fc00000| Complete 
| 253|0x000000008fd00000, 0x000000008fe00000, 0x000000008fe00000|100%| E|CS|TAMS 0x000000008fd00000| PB 0x000000008fd00000| Complete 
| 254|0x000000008fe00000, 0x000000008ff00000, 0x000000008ff00000|100%| E|CS|TAMS 0x000000008fe00000| PB 0x000000008fe00000| Complete 
| 255|0x000000008ff00000, 0x0000000090000000, 0x0000000090000000|100%| E|CS|TAMS 0x000000008ff00000| PB 0x000000008ff00000| Complete 
| 256|0x0000000090000000, 0x0000000090100000, 0x0000000090100000|100%| E|CS|TAMS 0x0000000090000000| PB 0x0000000090000000| Complete 
| 257|0x0000000090100000, 0x0000000090200000, 0x0000000090200000|100%| E|CS|TAMS 0x0000000090100000| PB 0x0000000090100000| Complete 
| 258|0x0000000090200000, 0x0000000090300000, 0x0000000090300000|100%| E|CS|TAMS 0x0000000090200000| PB 0x0000000090200000| Complete 
| 259|0x0000000090300000, 0x0000000090400000, 0x0000000090400000|100%| E|CS|TAMS 0x0000000090300000| PB 0x0000000090300000| Complete 
| 260|0x0000000090400000, 0x0000000090500000, 0x0000000090500000|100%| E|CS|TAMS 0x0000000090400000| PB 0x0000000090400000| Complete 
| 261|0x0000000090500000, 0x0000000090600000, 0x0000000090600000|100%| E|CS|TAMS 0x0000000090500000| PB 0x0000000090500000| Complete 
| 262|0x0000000090600000, 0x0000000090700000, 0x0000000090700000|100%| E|CS|TAMS 0x0000000090600000| PB 0x0000000090600000| Complete 
| 263|0x0000000090700000, 0x0000000090800000, 0x0000000090800000|100%| E|CS|TAMS 0x0000000090700000| PB 0x0000000090700000| Complete 
| 264|0x0000000090800000, 0x0000000090900000, 0x0000000090900000|100%| E|CS|TAMS 0x0000000090800000| PB 0x0000000090800000| Complete 
| 265|0x0000000090900000, 0x0000000090a00000, 0x0000000090a00000|100%| E|CS|TAMS 0x0000000090900000| PB 0x0000000090900000| Complete 
| 266|0x0000000090a00000, 0x0000000090b00000, 0x0000000090b00000|100%| E|CS|TAMS 0x0000000090a00000| PB 0x0000000090a00000| Complete 
| 267|0x0000000090b00000, 0x0000000090c00000, 0x0000000090c00000|100%| E|CS|TAMS 0x0000000090b00000| PB 0x0000000090b00000| Complete 
| 268|0x0000000090c00000, 0x0000000090d00000, 0x0000000090d00000|100%| E|CS|TAMS 0x0000000090c00000| PB 0x0000000090c00000| Complete 
| 269|0x0000000090d00000, 0x0000000090e00000, 0x0000000090e00000|100%| E|CS|TAMS 0x0000000090d00000| PB 0x0000000090d00000| Complete 
| 270|0x0000000090e00000, 0x0000000090f00000, 0x0000000090f00000|100%| E|CS|TAMS 0x0000000090e00000| PB 0x0000000090e00000| Complete 
| 271|0x0000000090f00000, 0x0000000091000000, 0x0000000091000000|100%| E|CS|TAMS 0x0000000090f00000| PB 0x0000000090f00000| Complete 
| 272|0x0000000091000000, 0x0000000091100000, 0x0000000091100000|100%| E|CS|TAMS 0x0000000091000000| PB 0x0000000091000000| Complete 
| 273|0x0000000091100000, 0x0000000091200000, 0x0000000091200000|100%| E|CS|TAMS 0x0000000091100000| PB 0x0000000091100000| Complete 
| 274|0x0000000091200000, 0x0000000091300000, 0x0000000091300000|100%| E|CS|TAMS 0x0000000091200000| PB 0x0000000091200000| Complete 
| 275|0x0000000091300000, 0x0000000091400000, 0x0000000091400000|100%| E|CS|TAMS 0x0000000091300000| PB 0x0000000091300000| Complete 
| 276|0x0000000091400000, 0x0000000091500000, 0x0000000091500000|100%| E|CS|TAMS 0x0000000091400000| PB 0x0000000091400000| Complete 
| 277|0x0000000091500000, 0x0000000091600000, 0x0000000091600000|100%| E|CS|TAMS 0x0000000091500000| PB 0x0000000091500000| Complete 
| 278|0x0000000091600000, 0x0000000091700000, 0x0000000091700000|100%| E|CS|TAMS 0x0000000091600000| PB 0x0000000091600000| Complete 
| 279|0x0000000091700000, 0x0000000091800000, 0x0000000091800000|100%| E|CS|TAMS 0x0000000091700000| PB 0x0000000091700000| Complete 
| 280|0x0000000091800000, 0x0000000091900000, 0x0000000091900000|100%| E|CS|TAMS 0x0000000091800000| PB 0x0000000091800000| Complete 
| 281|0x0000000091900000, 0x0000000091a00000, 0x0000000091a00000|100%| E|CS|TAMS 0x0000000091900000| PB 0x0000000091900000| Complete 
| 282|0x0000000091a00000, 0x0000000091b00000, 0x0000000091b00000|100%| E|CS|TAMS 0x0000000091a00000| PB 0x0000000091a00000| Complete 
| 283|0x0000000091b00000, 0x0000000091c00000, 0x0000000091c00000|100%| E|CS|TAMS 0x0000000091b00000| PB 0x0000000091b00000| Complete 
| 284|0x0000000091c00000, 0x0000000091d00000, 0x0000000091d00000|100%| E|CS|TAMS 0x0000000091c00000| PB 0x0000000091c00000| Complete 
| 285|0x0000000091d00000, 0x0000000091e00000, 0x0000000091e00000|100%| E|CS|TAMS 0x0000000091d00000| PB 0x0000000091d00000| Complete 
| 286|0x0000000091e00000, 0x0000000091f00000, 0x0000000091f00000|100%| E|CS|TAMS 0x0000000091e00000| PB 0x0000000091e00000| Complete 
| 351|0x0000000095f00000, 0x0000000096000000, 0x0000000096000000|100%| E|CS|TAMS 0x0000000095f00000| PB 0x0000000095f00000| Complete 
|2042|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| O|  |TAMS 0x00000000ffa00000| PB 0x00000000ffa00000| Untracked 
|2043|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| O|  |TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Untracked 
|2044|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| O|  |TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Untracked 
|2045|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| O|  |TAMS 0x00000000ffd00000| PB 0x00000000ffd00000| Untracked 
|2046|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| O|  |TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Untracked 
|2047|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| O|  |TAMS 0x00000000fff00000| PB 0x00000000fff00000| Untracked 

Card table byte_map: [0x00000202fe5b0000,0x00000202fe9b0000] _byte_map_base: 0x00000202fe1b0000

Marking Bits: (CMBitMap*) 0x00000202fbec0460
 Bits: [0x00000202973a0000, 0x00000202993a0000)

Polling page: 0x00000202fbf30000

Metaspace:

Usage:
  Non-class:    101.59 MB used.
      Class:     15.95 MB used.
       Both:    117.55 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,     102.75 MB ( 80%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      17.12 MB (  2%) committed,  1 nodes.
             Both:        1.12 GB reserved,     119.88 MB ( 10%) committed. 

Chunk freelists:
   Non-Class:  8.62 MB
       Class:  14.82 MB
        Both:  23.44 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 198.56 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 11.
num_arena_births: 4350.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1917.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 11.
num_chunks_taken_from_freelist: 9759.
num_chunk_merges: 11.
num_chunk_splits: 6339.
num_chunks_enlarged: 3957.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=10591Kb max_used=10632Kb free=109408Kb
 bounds [0x000002028fe70000, 0x0000020290930000, 0x00000202973a0000]
CodeHeap 'profiled nmethods': size=120000Kb used=26970Kb max_used=28284Kb free=93029Kb
 bounds [0x00000202883a0000, 0x0000020289f50000, 0x000002028f8d0000]
CodeHeap 'non-nmethods': size=5760Kb used=3025Kb max_used=3080Kb free=2734Kb
 bounds [0x000002028f8d0000, 0x000002028fbe0000, 0x000002028fe70000]
 total_blobs=15524 nmethods=14440 adapters=986
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 26.393 Thread 0x00000202ff4c57e0 nmethod 17743 0x0000020289321390 code [0x0000020289321540, 0x0000020289321758]
Event: 26.393 Thread 0x00000202ff4c57e0 17742   !   2       java.io.ObjectInputStream::readObject (160 bytes)
Event: 26.394 Thread 0x00000202ff4c57e0 nmethod 17742 0x0000020289042b90 code [0x0000020289042dc0, 0x0000020289043378]
Event: 26.394 Thread 0x00000202ff4c57e0 17744       2       java.io.ObjectInputStream$BlockDataInputStream::read (138 bytes)
Event: 26.395 Thread 0x00000202ff4c57e0 nmethod 17744 0x0000020289452710 code [0x0000020289452920, 0x0000020289452d88]
Event: 26.395 Thread 0x00000202ff4c57e0 17745       2       java.lang.invoke.LambdaForm$DMH/0x000002029d188000::invokeSpecial (15 bytes)
Event: 26.395 Thread 0x00000202ff4c57e0 nmethod 17745 0x0000020289005690 code [0x0000020289005840, 0x00000202890059a8]
Event: 26.396 Thread 0x00000202ff4c57e0 17746       2       java.io.ObjectStreamClass::hasWriteObjectData (9 bytes)
Event: 26.397 Thread 0x00000202ff4c57e0 nmethod 17746 0x0000020288f03810 code [0x0000020288f039c0, 0x0000020288f03b98]
Event: 26.397 Thread 0x00000202ff4c57e0 17747       2       java.io.SerialCallbackContext::check (59 bytes)
Event: 26.397 Thread 0x00000202ff4c57e0 nmethod 17747 0x0000020289072910 code [0x0000020289072b40, 0x0000020289072f78]
Event: 26.402 Thread 0x00000202ff4c57e0 17748       2       java.lang.invoke.LambdaForm$MH/0x000002029d000400::invoke (16 bytes)
Event: 26.402 Thread 0x00000202ff4c57e0 nmethod 17748 0x0000020288f16b10 code [0x0000020288f16cc0, 0x0000020288f16e60]
Event: 26.403 Thread 0x00000202ff4c57e0 17749 %     3       java.util.zip.ZipFile$Source::initCEN @ 261 (612 bytes)
Event: 26.407 Thread 0x00000202ff4c57e0 nmethod 17749% 0x0000020289d0e690 code [0x0000020289d0ec20, 0x0000020289d12200]
Event: 26.417 Thread 0x00000202ff4c57e0 17750       2       java.util.regex.Pattern$SliceU::match (99 bytes)
Event: 26.418 Thread 0x00000202ff4c57e0 nmethod 17750 0x0000020288f45090 code [0x0000020288f452a0, 0x0000020288f45618]
Event: 26.422 Thread 0x00000202ff4c57e0 17751       2       org.gradle.api.internal.CompositeDomainObjectSet::iterator (8 bytes)
Event: 26.422 Thread 0x00000202ff4c57e0 nmethod 17751 0x0000020289334390 code [0x0000020289334560, 0x0000020289334738]
Event: 26.423 Thread 0x00000202ff4c57e0 17752       3       java.util.zip.ZipFile$Source::initCEN (612 bytes)

GC Heap History (20 events):
Event: 18.704 GC heap before
{Heap before GC invocations=38 (full 0):
 garbage-first heap   total 239616K, used 182513K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 56 young (57344K), 9 survivors (9216K)
 Metaspace       used 111154K, committed 113472K, reserved 1179648K
  class space    used 15207K, committed 16320K, reserved 1048576K
}
Event: 18.714 GC heap after
{Heap after GC invocations=39 (full 0):
 garbage-first heap   total 239616K, used 136063K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 111154K, committed 113472K, reserved 1179648K
  class space    used 15207K, committed 16320K, reserved 1048576K
}
Event: 19.554 GC heap before
{Heap before GC invocations=39 (full 0):
 garbage-first heap   total 239616K, used 214911K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 84 young (86016K), 6 survivors (6144K)
 Metaspace       used 112739K, committed 115072K, reserved 1179648K
  class space    used 15361K, committed 16512K, reserved 1048576K
}
Event: 19.565 GC heap after
{Heap after GC invocations=40 (full 0):
 garbage-first heap   total 239616K, used 141686K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 112739K, committed 115072K, reserved 1179648K
  class space    used 15361K, committed 16512K, reserved 1048576K
}
Event: 20.123 GC heap before
{Heap before GC invocations=40 (full 0):
 garbage-first heap   total 239616K, used 213366K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 82 young (83968K), 11 survivors (11264K)
 Metaspace       used 114412K, committed 116864K, reserved 1179648K
  class space    used 15679K, committed 16896K, reserved 1048576K
}
Event: 20.134 GC heap after
{Heap after GC invocations=41 (full 0):
 garbage-first heap   total 239616K, used 146127K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 114412K, committed 116864K, reserved 1179648K
  class space    used 15679K, committed 16896K, reserved 1048576K
}
Event: 20.821 GC heap before
{Heap before GC invocations=42 (full 0):
 garbage-first heap   total 280576K, used 215759K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 76 young (77824K), 7 survivors (7168K)
 Metaspace       used 114415K, committed 116864K, reserved 1179648K
  class space    used 15680K, committed 16896K, reserved 1048576K
}
Event: 20.832 GC heap after
{Heap after GC invocations=43 (full 0):
 garbage-first heap   total 280576K, used 149503K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 114415K, committed 116864K, reserved 1179648K
  class space    used 15680K, committed 16896K, reserved 1048576K
}
Event: 21.515 GC heap before
{Heap before GC invocations=43 (full 0):
 garbage-first heap   total 280576K, used 251903K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 108 young (110592K), 7 survivors (7168K)
 Metaspace       used 114697K, committed 117120K, reserved 1179648K
  class space    used 15700K, committed 16896K, reserved 1048576K
}
Event: 21.524 GC heap after
{Heap after GC invocations=44 (full 0):
 garbage-first heap   total 280576K, used 152912K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 114697K, committed 117120K, reserved 1179648K
  class space    used 15700K, committed 16896K, reserved 1048576K
}
Event: 21.877 GC heap before
{Heap before GC invocations=44 (full 0):
 garbage-first heap   total 280576K, used 251216K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 108 young (110592K), 11 survivors (11264K)
 Metaspace       used 114760K, committed 117184K, reserved 1179648K
  class space    used 15701K, committed 16896K, reserved 1048576K
}
Event: 21.887 GC heap after
{Heap after GC invocations=45 (full 0):
 garbage-first heap   total 280576K, used 153694K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 114760K, committed 117184K, reserved 1179648K
  class space    used 15701K, committed 16896K, reserved 1048576K
}
Event: 23.291 GC heap before
{Heap before GC invocations=45 (full 0):
 garbage-first heap   total 280576K, used 255070K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 108 young (110592K), 8 survivors (8192K)
 Metaspace       used 118686K, committed 121088K, reserved 1179648K
  class space    used 16132K, committed 17344K, reserved 1048576K
}
Event: 23.302 GC heap after
{Heap after GC invocations=46 (full 0):
 garbage-first heap   total 280576K, used 159294K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 13 young (13312K), 13 survivors (13312K)
 Metaspace       used 118686K, committed 121088K, reserved 1179648K
  class space    used 16132K, committed 17344K, reserved 1048576K
}
Event: 23.812 GC heap before
{Heap before GC invocations=47 (full 0):
 garbage-first heap   total 301056K, used 250430K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 102 young (104448K), 13 survivors (13312K)
 Metaspace       used 119120K, committed 121536K, reserved 1179648K
  class space    used 16182K, committed 17408K, reserved 1048576K
}
Event: 23.825 GC heap after
{Heap after GC invocations=48 (full 0):
 garbage-first heap   total 301056K, used 163066K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 119120K, committed 121536K, reserved 1179648K
  class space    used 16182K, committed 17408K, reserved 1048576K
}
Event: 24.423 GC heap before
{Heap before GC invocations=48 (full 0):
 garbage-first heap   total 301056K, used 272634K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 117 young (119808K), 9 survivors (9216K)
 Metaspace       used 119422K, committed 121920K, reserved 1179648K
  class space    used 16221K, committed 17472K, reserved 1048576K
}
Event: 24.434 GC heap after
{Heap after GC invocations=49 (full 0):
 garbage-first heap   total 301056K, used 163226K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 119422K, committed 121920K, reserved 1179648K
  class space    used 16221K, committed 17472K, reserved 1048576K
}
Event: 25.182 GC heap before
{Heap before GC invocations=49 (full 0):
 garbage-first heap   total 301056K, used 271770K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 118 young (120832K), 11 survivors (11264K)
 Metaspace       used 119479K, committed 121984K, reserved 1179648K
  class space    used 16221K, committed 17472K, reserved 1048576K
}
Event: 25.193 GC heap after
{Heap after GC invocations=50 (full 0):
 garbage-first heap   total 301056K, used 166173K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 119479K, committed 121984K, reserved 1179648K
  class space    used 16221K, committed 17472K, reserved 1048576K
}

Dll operation events (15 events):
Event: 0.007 Loaded shared library C:\Program Files\Java\jdk-21\bin\java.dll
Event: 0.039 Loaded shared library C:\Program Files\Java\jdk-21\bin\jsvml.dll
Event: 0.079 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.082 Loaded shared library C:\Program Files\Java\jdk-21\bin\instrument.dll
Event: 0.086 Loaded shared library C:\Program Files\Java\jdk-21\bin\net.dll
Event: 0.087 Loaded shared library C:\Program Files\Java\jdk-21\bin\nio.dll
Event: 0.090 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.347 Loaded shared library C:\Program Files\Java\jdk-21\bin\jimage.dll
Event: 0.496 Loaded shared library C:\Program Files\Java\jdk-21\bin\verify.dll
Event: 0.626 Loaded shared library C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
Event: 0.631 Loaded shared library C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
Event: 1.734 Loaded shared library C:\Program Files\Java\jdk-21\bin\management.dll
Event: 1.736 Loaded shared library C:\Program Files\Java\jdk-21\bin\management_ext.dll
Event: 1.932 Loaded shared library C:\Program Files\Java\jdk-21\bin\extnet.dll
Event: 2.053 Loaded shared library C:\Program Files\Java\jdk-21\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 26.314 Thread 0x00000202e9849d20 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000202907f26c8 relative=0x0000000000001d68
Event: 26.314 Thread 0x00000202e9849d20 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000202907f26c8 method=org.gradle.internal.snapshot.ChildMap$Entry.handlePath(Lorg/gradle/internal/snapshot/VfsRelativePath;Lorg/gradle/internal/snapshot/CaseSensitivity;Lorg/gradle/internal/s
Event: 26.314 Thread 0x00000202e9849d20 DEOPT PACKING pc=0x00000202907f26c8 sp=0x0000008967afc7e0
Event: 26.314 Thread 0x00000202e9849d20 DEOPT UNPACKING pc=0x000002028f9246a2 sp=0x0000008967afc768 mode 2
Event: 26.314 Thread 0x00000202e9849d20 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000202907f26c8 relative=0x0000000000001d68
Event: 26.314 Thread 0x00000202e9849d20 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000202907f26c8 method=org.gradle.internal.snapshot.ChildMap$Entry.handlePath(Lorg/gradle/internal/snapshot/VfsRelativePath;Lorg/gradle/internal/snapshot/CaseSensitivity;Lorg/gradle/internal/s
Event: 26.314 Thread 0x00000202e9849d20 DEOPT PACKING pc=0x00000202907f26c8 sp=0x0000008967afc5a0
Event: 26.314 Thread 0x00000202e9849d20 DEOPT UNPACKING pc=0x000002028f9246a2 sp=0x0000008967afc528 mode 2
Event: 26.314 Thread 0x00000202e9849d20 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000202907f26c8 relative=0x0000000000001d68
Event: 26.314 Thread 0x00000202e9849d20 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000202907f26c8 method=org.gradle.internal.snapshot.ChildMap$Entry.handlePath(Lorg/gradle/internal/snapshot/VfsRelativePath;Lorg/gradle/internal/snapshot/CaseSensitivity;Lorg/gradle/internal/s
Event: 26.314 Thread 0x00000202e9849d20 DEOPT PACKING pc=0x00000202907f26c8 sp=0x0000008967afc5a0
Event: 26.314 Thread 0x00000202e9849d20 DEOPT UNPACKING pc=0x000002028f9246a2 sp=0x0000008967afc528 mode 2
Event: 26.331 Thread 0x00000202e984aa40 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000202908fb640 relative=0x0000000000003720
Event: 26.331 Thread 0x00000202e984aa40 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000202908fb640 method=org.gradle.internal.fingerprint.classpath.impl.ClasspathFingerprintingStrategy$ClasspathFingerprintingVisitor.visitEntry(Lorg/gradle/internal/snapshot/FileSystemLocation
Event: 26.331 Thread 0x00000202e984aa40 DEOPT PACKING pc=0x00000202908fb640 sp=0x00000089678fb960
Event: 26.331 Thread 0x00000202e984aa40 DEOPT UNPACKING pc=0x000002028f9246a2 sp=0x00000089678fb920 mode 2
Event: 26.393 Thread 0x00000202e98475c0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000020290029adc relative=0x0000000000003cfc
Event: 26.393 Thread 0x00000202e98475c0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000020290029adc method=java.util.zip.ZipFile$Source.initCEN(I)V @ 16 c2
Event: 26.393 Thread 0x00000202e98475c0 DEOPT PACKING pc=0x0000020290029adc sp=0x0000008967bfa600
Event: 26.393 Thread 0x00000202e98475c0 DEOPT UNPACKING pc=0x000002028f9246a2 sp=0x0000008967bfa608 mode 2

Classes loaded (20 events):
Event: 26.168 Loading class java/nio/file/FileTreeIterator
Event: 26.168 Loading class java/nio/file/FileTreeIterator done
Event: 26.209 Loading class java/nio/channels/Channels$WritableByteChannelImpl
Event: 26.209 Loading class java/nio/channels/Channels$WritableByteChannelImpl done
Event: 26.211 Loading class java/util/Collections$UnmodifiableSortedMap
Event: 26.211 Loading class java/util/Collections$UnmodifiableSortedMap done
Event: 26.211 Loading class java/nio/StringCharBuffer
Event: 26.212 Loading class java/nio/StringCharBuffer done
Event: 26.212 Loading class sun/nio/cs/ThreadLocalCoders
Event: 26.212 Loading class sun/nio/cs/ThreadLocalCoders done
Event: 26.212 Loading class sun/nio/cs/ThreadLocalCoders$1
Event: 26.212 Loading class sun/nio/cs/ThreadLocalCoders$Cache
Event: 26.212 Loading class sun/nio/cs/ThreadLocalCoders$Cache done
Event: 26.212 Loading class sun/nio/cs/ThreadLocalCoders$1 done
Event: 26.212 Loading class sun/nio/cs/ThreadLocalCoders$2
Event: 26.212 Loading class sun/nio/cs/ThreadLocalCoders$2 done
Event: 26.264 Loading class java/util/zip/ZipFile$ZipEntryIterator
Event: 26.268 Loading class java/util/zip/ZipFile$ZipEntryIterator done
Event: 26.274 Loading class java/util/zip/ZipOutputStream
Event: 26.274 Loading class java/util/zip/ZipOutputStream done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 23.751 Thread 0x00000202e9843ab0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008cd40810}: static Lcom/android/build/api/variant/impl/VariantOutputConfigurationImpl;.<clinit>()V> (0x000000008cd40810) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 1106]
Event: 24.063 Thread 0x00000202e984a3b0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008f52c998}> (0x000000008f52c998) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.074 Thread 0x00000202e984a3b0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008f558a18}> (0x000000008f558a18) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.099 Thread 0x00000202e9848970 Exception <a 'sun/nio/fs/WindowsException'{0x000000008f418330}> (0x000000008f418330) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.265 Thread 0x00000202e984aa40 Exception <a 'sun/nio/fs/WindowsException'{0x000000008db81c68}> (0x000000008db81c68) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.265 Thread 0x00000202e984aa40 Exception <a 'sun/nio/fs/WindowsException'{0x000000008db83d28}> (0x000000008db83d28) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.274 Thread 0x00000202e984aa40 Exception <a 'sun/nio/fs/WindowsException'{0x000000008dba8968}> (0x000000008dba8968) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.274 Thread 0x00000202e984aa40 Exception <a 'sun/nio/fs/WindowsException'{0x000000008dbaabc8}> (0x000000008dbaabc8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.179 Thread 0x00000202e9846f30 Exception <a 'sun/nio/fs/WindowsException'{0x000000008db238f8}> (0x000000008db238f8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.191 Thread 0x00000202e9846f30 Exception <a 'java/lang/NoSuchMethodError'{0x000000008da96128}: 'int java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008da96128) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 26.206 Thread 0x00000202e9846f30 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d9ec948}> (0x000000008d9ec948) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.220 Thread 0x00000202e9846f30 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d877d28}> (0x000000008d877d28) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.227 Thread 0x00000202e9849d20 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d8db1e0}> (0x000000008d8db1e0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.268 Thread 0x00000202e98475c0 Implicit null exception at 0x0000020290185ae6 to 0x0000020290186670
Event: 26.312 Thread 0x00000202e9849d20 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d2cbc28}> (0x000000008d2cbc28) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.322 Thread 0x00000202e984aa40 Exception <a 'sun/nio/fs/WindowsException'{0x0000000090e9d4b8}> (0x0000000090e9d4b8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.330 Thread 0x00000202e984aa40 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d190880}> (0x000000008d190880) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.333 Thread 0x00000202e984aa40 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d0436f0}> (0x000000008d0436f0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.339 Thread 0x00000202e984aa40 Exception <a 'sun/nio/fs/WindowsException'{0x000000008cf0b360}> (0x000000008cf0b360) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.423 Thread 0x00000202ee35c600 Exception <a 'java/lang/OutOfMemoryError'{0x000000008c760008}> (0x000000008c760008) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 539]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 23.540 Executing VM operation: G1PauseCleanup
Event: 23.540 Executing VM operation: G1PauseCleanup done
Event: 23.812 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 23.827 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 24.422 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 24.434 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 25.182 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 25.193 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 25.378 Executing VM operation: G1PauseRemark
Event: 25.395 Executing VM operation: G1PauseRemark done
Event: 25.489 Executing VM operation: G1PauseCleanup
Event: 25.490 Executing VM operation: G1PauseCleanup done
Event: 26.160 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 26.161 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 26.164 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 26.165 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 26.400 Executing VM operation: ICBufferFull
Event: 26.400 Executing VM operation: ICBufferFull done
Event: 26.400 Executing VM operation: ICBufferFull
Event: 26.400 Executing VM operation: ICBufferFull done

Events (20 events):
Event: 25.394 Thread 0x00000202ff4991e0 flushing nmethod 0x0000020290306390
Event: 25.394 Thread 0x00000202ff4991e0 flushing nmethod 0x000002029012b290
Event: 25.394 Thread 0x00000202ff4991e0 flushing nmethod 0x0000020290292410
Event: 25.394 Thread 0x00000202ff4991e0 flushing nmethod 0x0000020290235b90
Event: 25.394 Thread 0x00000202ff4991e0 flushing nmethod 0x000002029027b010
Event: 25.394 Thread 0x00000202ff4991e0 flushing nmethod 0x0000020290262d10
Event: 25.394 Thread 0x00000202ff4991e0 flushing nmethod 0x0000020290248e90
Event: 25.394 Thread 0x00000202ff4991e0 flushing nmethod 0x0000020290208990
Event: 25.394 Thread 0x00000202ff4991e0 flushing nmethod 0x00000202900c0090
Event: 25.394 Thread 0x00000202ff4991e0 flushing nmethod 0x00000202900d3590
Event: 25.394 Thread 0x00000202ff4991e0 flushing nmethod 0x0000020290074a10
Event: 25.394 Thread 0x00000202ff4991e0 flushing nmethod 0x000002029007db10
Event: 25.395 Thread 0x00000202ff4991e0 flushing nmethod 0x000002029007c690
Event: 25.395 Thread 0x00000202ff4991e0 flushing nmethod 0x0000020290075910
Event: 25.395 Thread 0x00000202ff4991e0 flushing nmethod 0x0000020290016a10
Event: 25.395 Thread 0x00000202ff4991e0 flushing nmethod 0x000002028ff8db10
Event: 25.395 Thread 0x00000202ff4991e0 flushing nmethod 0x000002028ffa7110
Event: 25.395 Thread 0x00000202ff4991e0 flushing nmethod 0x000002028ff42490
Event: 26.150 Thread 0x00000202ee35c600 Thread added: 0x00000202ee35c600
Event: 26.391 Thread 0x00000202ee35cc90 Thread added: 0x00000202ee35cc90


Dynamic libraries:
0x00007ff68f980000 - 0x00007ff68f990000 	C:\Program Files\Java\jdk-21\bin\java.exe
0x00007fffd2a70000 - 0x00007fffd2c68000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fffd0f10000 - 0x00007fffd0fd2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fffd02b0000 - 0x00007fffd05a6000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fffd0710000 - 0x00007fffd0810000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff9ffb0000 - 0x00007fff9ffc9000 	C:\Program Files\Java\jdk-21\bin\jli.dll
0x00007fff9fb90000 - 0x00007fff9fbab000 	C:\Program Files\Java\jdk-21\bin\VCRUNTIME140.dll
0x00007fffd1e80000 - 0x00007fffd1f31000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fffd1100000 - 0x00007fffd119e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fffd1270000 - 0x00007fffd130f000 	C:\WINDOWS\System32\sechost.dll
0x00007fffd1cc0000 - 0x00007fffd1de3000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fffd01f0000 - 0x00007fffd0217000 	C:\WINDOWS\System32\bcrypt.dll
0x00007fffd1b10000 - 0x00007fffd1cad000 	C:\WINDOWS\System32\USER32.dll
0x00007fffd09b0000 - 0x00007fffd09d2000 	C:\WINDOWS\System32\win32u.dll
0x00007fffbefc0000 - 0x00007fffbf25a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007fffd1310000 - 0x00007fffd133b000 	C:\WINDOWS\System32\GDI32.dll
0x00007fffd0890000 - 0x00007fffd09a9000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fffd0100000 - 0x00007fffd019d000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fffc7ef0000 - 0x00007fffc7efa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fffd1f40000 - 0x00007fffd1f6f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007fffa75f0000 - 0x00007fffa75fc000 	C:\Program Files\Java\jdk-21\bin\vcruntime140_1.dll
0x00007fff8c6e0000 - 0x00007fff8c76e000 	C:\Program Files\Java\jdk-21\bin\msvcp140.dll
0x00007fff7d700000 - 0x00007fff7e417000 	C:\Program Files\Java\jdk-21\bin\server\jvm.dll
0x00007fffd1090000 - 0x00007fffd10fb000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fffcff60000 - 0x00007fffcffab000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007fffc4da0000 - 0x00007fffc4dc7000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fffcff40000 - 0x00007fffcff52000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007fffce970000 - 0x00007fffce982000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fffa7190000 - 0x00007fffa719a000 	C:\Program Files\Java\jdk-21\bin\jimage.dll
0x00007fffce700000 - 0x00007fffce901000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fffbe6d0000 - 0x00007fffbe704000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fffd0220000 - 0x00007fffd02a2000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fffa3cc0000 - 0x00007fffa3ccf000 	C:\Program Files\Java\jdk-21\bin\instrument.dll
0x00007fff9d380000 - 0x00007fff9d39f000 	C:\Program Files\Java\jdk-21\bin\java.dll
0x00007fffd1340000 - 0x00007fffd1aae000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fffcdf50000 - 0x00007fffce6f3000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fffd26b0000 - 0x00007fffd2a03000 	C:\WINDOWS\System32\combase.dll
0x00007fffcfa60000 - 0x00007fffcfa8b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007fffd11a0000 - 0x00007fffd126d000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fffd0fe0000 - 0x00007fffd108d000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fffd2180000 - 0x00007fffd21db000 	C:\WINDOWS\System32\shlwapi.dll
0x00007fffd0030000 - 0x00007fffd0055000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff8c600000 - 0x00007fff8c6d7000 	C:\Program Files\Java\jdk-21\bin\jsvml.dll
0x00007fff9d340000 - 0x00007fff9d358000 	C:\Program Files\Java\jdk-21\bin\zip.dll
0x00007fffa6aa0000 - 0x00007fffa6ab0000 	C:\Program Files\Java\jdk-21\bin\net.dll
0x00007fffcc310000 - 0x00007fffcc41a000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007fffcf7c0000 - 0x00007fffcf82a000 	C:\WINDOWS\system32\mswsock.dll
0x00007fff9d360000 - 0x00007fff9d376000 	C:\Program Files\Java\jdk-21\bin\nio.dll
0x00007fffa63e0000 - 0x00007fffa63f0000 	C:\Program Files\Java\jdk-21\bin\verify.dll
0x00007fffc0060000 - 0x00007fffc0087000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007fffa0fe0000 - 0x00007fffa1124000 	C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007fffa59b0000 - 0x00007fffa59ba000 	C:\Program Files\Java\jdk-21\bin\management.dll
0x00007fffa4e40000 - 0x00007fffa4e4b000 	C:\Program Files\Java\jdk-21\bin\management_ext.dll
0x00007fffd0b40000 - 0x00007fffd0b48000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007fffcf9b0000 - 0x00007fffcf9c8000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fffcf0e0000 - 0x00007fffcf118000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fffcffb0000 - 0x00007fffcffde000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fffcf9d0000 - 0x00007fffcf9dc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fffcf4a0000 - 0x00007fffcf4db000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fffd1ff0000 - 0x00007fffd1ff8000 	C:\WINDOWS\System32\NSI.dll
0x00007fffa3f20000 - 0x00007fffa3f29000 	C:\Program Files\Java\jdk-21\bin\extnet.dll
0x00007fffa8c10000 - 0x00007fffa8c1e000 	C:\Program Files\Java\jdk-21\bin\sunmscapi.dll
0x00007fffd05b0000 - 0x00007fffd070d000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007fffcfad0000 - 0x00007fffcfaf7000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007fffcfa90000 - 0x00007fffcfacb000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007fffc0050000 - 0x00007fffc0057000 	C:\WINDOWS\system32\wshunix.dll
0x00007fffbbbd0000 - 0x00007fffbbbe7000 	C:\WINDOWS\system32\napinsp.dll
0x00007fffbbba0000 - 0x00007fffbbbbb000 	C:\WINDOWS\system32\pnrpnsp.dll
0x00007fffbb8d0000 - 0x00007fffbb8ed000 	C:\WINDOWS\system32\wshbth.dll
0x00007fffcbf20000 - 0x00007fffcbf3d000 	C:\WINDOWS\system32\NLAapi.dll
0x00007fffcf4e0000 - 0x00007fffcf5aa000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007fffbbb40000 - 0x00007fffbbb52000 	C:\WINDOWS\System32\winrnr.dll
0x00007fffc7ee0000 - 0x00007fffc7eea000 	C:\Windows\System32\rasadhlp.dll
0x00007fffc6e60000 - 0x00007fffc6ee0000 	C:\WINDOWS\System32\fwpuclnt.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-21\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Program Files\Java\jdk-21\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\gradle-daemon-main-8.11.1.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 369098752                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Users\<USER>\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\local\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Python313\Scripts;C:\Program Files\Python313;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\usr\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Void\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\Program Files\JetBrains\PhpStorm 2024.1.3\bin;C:\dart-sdk\bin;C:\flutter sdk\flutter\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Genymobile.scrcpy_Microsoft.Winget.Source_8wekyb3d8bbwe\scrcpy-win64-v3.1;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Program Files\Git\usr\bin\vendor_perl;C:\Program Files\Git\usr\bin\core_perl
USERNAME=ntc
SHELL=C:\Program Files\Git\usr\bin\bash.exe
DISPLAY=needs-to-be-defined
LANG=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 0 days 16:14 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (4027M free)
TotalPageFile size 22476M (AvailPageFile size 69M)
current process WorkingSet (physical memory assigned to process): 658M, peak: 666M
current process commit charge ("private bytes"): 701M, peak: 710M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58) for windows-amd64 JRE (21.0.2+13-LTS-58), built on 2024-01-05T18:32:24Z by "mach5one" with MS VC++ 17.1 (VS2022)

END.
