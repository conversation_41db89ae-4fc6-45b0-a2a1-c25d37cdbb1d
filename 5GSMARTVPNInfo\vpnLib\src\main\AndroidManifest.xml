<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <application>
        <service
            android:name=".core.OpenVPNService"
            android:exported="true"
            android:foregroundServiceType="dataSync"
            android:permission="android.permission.BIND_VPN_SERVICE">
            <intent-filter>
                <action android:name="android.net.VpnService" />
            </intent-filter>
        </service>

        <!-- VpnService-based OpenVPN implementation (V2) -->
        <service
            android:name=".core.OpenVPNServiceV2"
            android:exported="true"
            android:foregroundServiceType="dataSync"
            android:permission="android.permission.BIND_VPN_SERVICE">
            <intent-filter>
                <action android:name="android.net.VpnService" />
            </intent-filter>
        </service>
    </application>
</manifest>
