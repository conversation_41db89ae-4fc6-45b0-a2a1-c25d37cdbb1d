<?php
/**
 * Fix Singapore Server Configuration
 * Fixes the VPN profile validation error for the existing Surfshark server
 */

require_once 'includes/config.php';

echo "<h1>🇸🇬 Fix Singapore Server Configuration</h1>\n";
echo "<p>Fix the VPN profile validation error for your existing Surfshark Singapore server</p>\n";
echo "<hr>\n";

echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>\n";
echo "<h4>✅ Good News!</h4>\n";
echo "<p>You already have a <strong>real working Surfshark server</strong> in your database:</p>\n";
echo "<ul>\n";
echo "<li><strong>Server:</strong> sg-sng.prod.surfshark.com:1194</li>\n";
echo "<li><strong>Provider:</strong> Surfshark (legitimate VPN service)</li>\n";
echo "<li><strong>Certificates:</strong> CA certificate and TLS-auth included</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<div style='background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0;'>\n";
echo "<h4>❌ The Issue</h4>\n";
echo "<p><strong>Problem:</strong> VPN profile validation failed: **********</p>\n";
echo "<p><strong>Cause:</strong> Android VPN service requires client certificate, but your config uses username/password authentication</p>\n";
echo "<p><strong>Solution:</strong> Convert to Android-compatible format</p>\n";
echo "</div>\n";

// Get current Singapore server configuration
try {
    $result = $conn->query("SELECT * FROM servers WHERE name = 'Singapore' LIMIT 1");
    if ($result && $result->num_rows > 0) {
        $server = $result->fetch_assoc();
        
        echo "<h2>Current Singapore Server Configuration</h2>\n";
        echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;'>\n";
        echo "<p><strong>Name:</strong> " . htmlspecialchars($server['name']) . "</p>\n";
        echo "<p><strong>Username:</strong> " . htmlspecialchars($server['username']) . "</p>\n";
        echo "<p><strong>Server:</strong> sg-sng.prod.surfshark.com:1194</p>\n";
        echo "<p><strong>Status:</strong> " . ($server['status'] ? 'Active' : 'Inactive') . "</p>\n";
        echo "</div>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: #dc3545;'>Error loading server: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>Fix Options</h2>\n";

echo "<h3>Option 1: Convert to Android-Compatible Format (Recommended)</h3>\n";

echo "<form method='post' style='margin: 20px 0;'>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;'>\n";
echo "<h4>🔧 Convert Singapore Server</h4>\n";
echo "<p>This will convert your Surfshark configuration to Android-compatible format by:</p>\n";
echo "<ul>\n";
echo "<li>Keeping the same server and credentials</li>\n";
echo "<li>Removing incompatible directives</li>\n";
echo "<li>Adding proper DNS and routing for internet browsing</li>\n";
echo "<li>Using username/password authentication (compatible with Android)</li>\n";
echo "</ul>\n";
echo "<button type='submit' name='fix_singapore' style='background: #28a745; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px;'>Fix Singapore Server</button>\n";
echo "</div>\n";
echo "</form>\n";

// Handle the fix
if (isset($_POST['fix_singapore'])) {
    try {
        // Create Android-compatible Surfshark configuration
        $androidConfig = "client
dev tun
proto udp
remote sg-sng.prod.surfshark.com 1194
resolv-retry infinite
nobind
persist-key
persist-tun
cipher AES-256-CBC
auth SHA256
verb 3
mute 20
auth-user-pass

# DNS Configuration for internet browsing
dhcp-option DNS *******
dhcp-option DNS *******
dhcp-option DNS *******

# Routing configuration
redirect-gateway def1 bypass-dhcp

# Connection settings
keepalive 10 120
ping-timer-rem
persist-tun
persist-key

# CA Certificate (from your original config)
<ca>
-----BEGIN CERTIFICATE-----
MIIFTTCCAzWgAwIBAgIJAMs9S3fqwv+mMA0GCSqGSIb3DQEBCwUAMD0xCzAJBgNV
BAYTAlZHMRIwEAYDVQQKDAlTdXJmc2hhcmsxGjAYBgNVBAMMEVN1cmZzaGFyayBS
b290IENBMB4XDTE4MDMxNDA4NTkyM1oXDTI4MDMxMTA4NTkyM1owPTELMAkGA1UE
BhMCVkcxEjAQBgNVBAoMCVN1cmZzaGFyazEaMBgGA1UEAwwRU3VyZnNoYXJrIFJv
b3QgQ0EwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQDEGMNj0aisM63o
SkmVJyZPaYX7aPsZtzsxo6m6p5Wta3MGASoryRsBuRaH6VVa0fwbI1nw5ubyxkua
Na4v3zHVwuSq6F1p8S811+1YP1av+jqDcMyojH0ujZSHIcb/i5LtaHNXBQ3qN48C
c7sqBnTIIFpmb5HthQ/4pW+a82b1guM5dZHsh7q+LKQDIGmvtMtO1+NEnmj81BAp
FayiaD1ggvwDI4x7o/Y3ksfWSCHnqXGyqzSFLh8QuQrTmWUm84YHGFxoI1/8AKdI
yVoB6BjcaMKtKs/pbctk6vkzmYf0XmGovDKPQF6MwUekchLjB5gSBNnptSQ9kNgn
TLqi0OpSwI6ixX52Ksva6UM8P01ZIhWZ6ua/T/tArgODy5JZMW+pQ1A6L0b7egIe
ghpwKnPRG+5CzgO0J5UE6gv000mqbmC3CbiS8xi2xuNgruAyY2hUOoV9/BuBev8t
tE5ZCsJH3YlG6NtbZ9hPc61GiBSx8NJnX5QHyCnfic/X87eST/amZsZCAOJ5v4EP
SaKrItt+HrEFWZQIq4fJmHJNNbYvWzCE08AL+5/6Z+lxb/Bm3dapx2zdit3x2e+m
iGHekuiE8lQWD0rXD4+T+nDRi3X+kyt8Ex/8qRiUfrisrSHFzVMRungIMGdO9O/z
CINFrb7wahm4PqU2f12Z9TRCOTXciQIDAQABo1AwTjAdBgNVHQ4EFgQUYRpbQwyD
ahLMN3F2ony3+UqOYOgwHwYDVR0jBBgwFoAUYRpbQwyDahLMN3F2ony3+UqOYOgw
DAYDR0TBAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAgEAn9zV7F/XVnFNZhHFrt0Z
S1Yqz+qM9CojLmiyblMFh0p7t+Hh+VKVgMwrz0LwDH4UsOosXA28eJPmech6/bjf
ymkoXISy/NUSTFpUChGO9RabGGxJsT4dugOw9MPaIVZffny4qYOc/rXDXDSfF2b+
303lLPI43y9qoe0oyZ1vtk/UKG75FkWfFUogGNbpOkuz+et5Y0aIEiyg0yh6/l5Q
5h8+yom0HZnREHhqieGbkaGKLkyu7zQ4D4tRK/mBhd8nv+09GtPEG+D5LPbabFVx
KjBMP4Vp24WuSUOqcGSsURHevawPVBfgmsxf1UCjelaIwngdh6WfNCRXa5QQPQTK
ubQvkvXONCDdhmdXQccnRX1nJWhPYi0onffvjsWUfztRypsKzX4dvM9k7xnIcGSG
EnCC4RCgt1UiZIj7frcCMssbA6vJ9naM0s7JF7N3VKeHJtqe1OCRHMYnWUZt9vrq
X6IoIHlZCoLlv39wFW9QNxelcAOCVbD+19MZ0ZXt7LitjIqe7yF5WxDQN4xru087
FzQ4Hfj7eH1SNLLyKZkA1eecjmRoi/OoqAt7afSnwtQLtMUc2bQDg6rHt5C0e4dC
LqP/9PGZTSJiwmtRHJ/N5qYWIh9ju83APvLm/AGBTR2pXmj9G3KdVOkpIC7L35dI
623cSEC3Q3UZutsEm/UplsM=
-----END CERTIFICATE-----
</ca>";

        // Update both Singapore server and Working Test Server
        $updateSql = "UPDATE servers SET configFile = ? WHERE name IN ('Singapore', 'Working Test Server')";
        $stmt = $conn->prepare($updateSql);
        $stmt->bind_param('s', $androidConfig);

        if ($stmt->execute()) {
            $affectedRows = $stmt->affected_rows;
            echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>\n";
            echo "<h4>✅ Servers Fixed Successfully!</h4>\n";
            echo "<p><strong>Updated $affectedRows server(s) with Android-compatible configuration</strong></p>\n";
            echo "<p><strong>Changes Made:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>Converted to Android-compatible format</li>\n";
            echo "<li>Removed incompatible directives (tls-auth, remote-random, etc.)</li>\n";
            echo "<li>Added proper DNS servers for internet browsing</li>\n";
            echo "<li>Added route redirection for all traffic</li>\n";
            echo "<li>Kept original Surfshark server and credentials</li>\n";
            echo "<li>Updated both Singapore and Working Test Server</li>\n";
            echo "</ul>\n";
            echo "<p><strong>Next Steps:</strong></p>\n";
            echo "<ol>\n";
            echo "<li>Test the Android app again</li>\n";
            echo "<li>The VPN profile validation error should be resolved</li>\n";
            echo "<li>You should see successful connection to Singapore server</li>\n";
            echo "<li>Internet browsing should work through VPN</li>\n";
            echo "</ol>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0;'>\n";
            echo "<h4>❌ Update Failed</h4>\n";
            echo "<p>Error: " . $conn->error . "</p>\n";
            echo "</div>\n";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0;'>\n";
        echo "<h4>❌ Fix Error</h4>\n";
        echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "</div>\n";
    }
}

echo "<h3>Option 2: Alternative Servers</h3>\n";

echo "<div style='background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0;'>\n";
echo "<h4>⚠️ If Singapore Server Still Doesn't Work</h4>\n";
echo "<p>You can also:</p>\n";
echo "<ul>\n";
echo "<li>Use your OVHcloud server with the <a href='fix_ovhcloud_config.php'>OVHcloud configuration tool</a></li>\n";
echo "<li>Set up a local OpenVPN server for testing</li>\n";
echo "<li>Try other Surfshark servers if you have access</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<h2>Why This Should Work</h2>\n";

echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #007cba; margin: 10px 0;'>\n";
echo "<h4>🔧 Technical Explanation</h4>\n";
echo "<p>The original configuration had these Android incompatibilities:</p>\n";
echo "<ul>\n";
echo "<li><code>remote-random</code> - Not supported by Android VPN service</li>\n";
echo "<li><code>tls-auth</code> - Causes validation issues on some Android versions</li>\n";
echo "<li><code>ping-restart 0</code> - Invalid parameter</li>\n";
echo "<li>Missing DNS and routing configuration</li>\n";
echo "</ul>\n";
echo "<p>The fixed configuration:</p>\n";
echo "<ul>\n";
echo "<li>Uses only Android-compatible directives</li>\n";
echo "<li>Includes proper DNS servers for internet browsing</li>\n";
echo "<li>Adds route redirection for all traffic</li>\n";
echo "<li>Keeps the working Surfshark server and credentials</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<p><strong>Navigation:</strong></p>\n";
echo "<ul>\n";
echo "<li><a href='validate_vpn_servers.php'>🔍 Validate Server Configurations</a></li>\n";
echo "<li><a href='fix_ovhcloud_config.php'>☁️ Configure OVHcloud Server</a></li>\n";
echo "<li><a href='test_vpn_connection_fixes.php'>🧪 Test API Endpoints</a></li>\n";
echo "<li><a href='index.php'>← Back to Admin Panel</a></li>\n";
echo "</ul>\n";
?>
