1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.official.fivegfastvpn"
4    android:versionCode="12"
5    android:versionName="12" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:4:5-67
11-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:5:5-79
12-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:5:22-76
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:6:5-77
13-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:6:22-74
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
14-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:7:5-87
14-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:7:22-84
15    <uses-permission android:name="com.android.vending.BILLING" />
15-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:8:5-67
15-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:8:22-64
16    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
16-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:9:5-77
16-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:9:22-74
17
18    <!-- VPN Service Permissions -->
19    <uses-permission android:name="android.permission.BIND_VPN_SERVICE" />
19-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:12:5-75
19-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:12:22-72
20    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
20-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:13:5-78
20-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:13:22-75
21    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
21-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:14:5-81
21-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:14:22-78
22
23    <queries>
23-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:12:5-19:15
24        <intent>
24-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:13:9-15:18
25            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
25-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:14:13-91
25-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:14:21-88
26        </intent>
27        <intent>
27-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:16:9-18:18
28            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
28-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:17:13-116
28-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:17:21-113
29        </intent>
30
31        <package android:name="com.facebook.katana" /> <!-- For browser content -->
31-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:12:9-55
31-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:12:18-52
32        <intent>
32-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:38:9-44:18
33            <action android:name="android.intent.action.VIEW" />
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
34
35            <category android:name="android.intent.category.BROWSABLE" />
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:13-74
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:23-71
36
37            <data android:scheme="https" />
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
38        </intent> <!-- End of browser content -->
39        <!-- For CustomTabsService -->
40        <intent>
40-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:47:9-49:18
41            <action android:name="android.support.customtabs.action.CustomTabsService" />
41-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:13-90
41-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:21-87
42        </intent> <!-- End of CustomTabsService -->
43        <!-- For MRAID capabilities -->
44        <intent>
44-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:52:9-56:18
45            <action android:name="android.intent.action.INSERT" />
45-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:13-67
45-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:21-64
46
47            <data android:mimeType="vnd.android.cursor.dir/event" />
47-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
48        </intent>
49        <intent>
49-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:57:9-61:18
50            <action android:name="android.intent.action.VIEW" />
50-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
50-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
51
52            <data android:scheme="sms" />
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
53        </intent>
54        <intent>
54-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:62:9-66:18
55            <action android:name="android.intent.action.DIAL" />
55-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:13-65
55-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:21-62
56
57            <data android:path="tel:" />
57-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
58        </intent>
59    </queries>
60
61    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
61-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
61-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:22-65
62    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
62-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
62-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
63    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
63-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:17:5-79
63-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:17:22-76
64    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
64-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:5-82
64-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:22-79
65    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
65-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:5-88
65-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:22-85
66    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
66-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:5-83
66-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:22-80
67
68    <permission
68-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
69        android:name="com.official.fivegfastvpn.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
69-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
70        android:protectionLevel="signature" />
70-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
71
72    <uses-permission android:name="com.official.fivegfastvpn.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
72-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
72-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
73
74    <application
74-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:15:5-96:15
75        android:name="com.official.fivegfastvpn.VPNApplication"
75-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:16:9-39
76        android:allowBackup="true"
76-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:17:9-35
77        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
77-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
78        android:dataExtractionRules="@xml/data_extraction_rules"
78-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:18:9-65
79        android:debuggable="true"
80        android:extractNativeLibs="true"
81        android:fullBackupContent="@xml/backup_rules"
81-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:19:9-54
82        android:icon="@mipmap/ic_launcher"
82-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:21:9-43
83        android:label="@string/app_name"
83-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:22:9-41
84        android:networkSecurityConfig="@xml/network_security_config"
84-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:20:9-69
85        android:roundIcon="@mipmap/ic_launcher_round"
85-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:23:9-54
86        android:supportsRtl="true"
86-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:24:9-35
87        android:testOnly="true"
88        android:theme="@style/Base.Theme._5GSMARTVPNInfo"
88-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:26:9-58
89        android:usesCleartextTraffic="true" >
89-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:25:9-44
90        <activity
90-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:28:9-30:40
91            android:name="com.official.fivegfastvpn.activity.About_Us"
91-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:29:13-46
92            android:exported="false" />
92-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:30:13-37
93        <activity
93-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:31:9-34:58
94            android:name="com.official.fivegfastvpn.activity.NotificationsActivity"
94-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:32:13-59
95            android:exported="false"
95-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:33:13-37
96            android:parentActivityName="com.official.fivegfastvpn.MainActivity" />
96-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:34:13-55
97        <activity
97-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:35:9-38:58
98            android:name="com.official.fivegfastvpn.api.ApiTestActivity"
98-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:36:13-48
99            android:exported="false"
99-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:37:13-37
100            android:parentActivityName="com.official.fivegfastvpn.MainActivity" />
100-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:38:13-55
101        <activity android:name="com.official.fivegfastvpn.pro.PremiumActivity" />
101-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:40:9-57
101-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:40:19-54
102        <activity android:name="com.official.fivegfastvpn.MainActivity" />
102-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:41:9-50
102-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:41:19-47
103        <activity android:name="com.official.fivegfastvpn.activity.ServersActivity" />
103-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:42:9-62
103-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:42:19-59
104        <activity
104-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:43:9-51:20
105            android:name="com.official.fivegfastvpn.SplashActivity"
105-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:44:13-43
106            android:exported="true" >
106-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:45:13-36
107            <intent-filter>
107-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:46:13-50:29
108                <action android:name="android.intent.action.MAIN" />
108-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:47:17-69
108-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:47:25-66
109
110                <category android:name="android.intent.category.LAUNCHER" />
110-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:49:17-77
110-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:49:27-74
111            </intent-filter>
112        </activity>
113        <activity
113-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:52:9-57:52
114            android:name="de.blinkt.openvpn.DisconnectVPNActivity"
114-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:53:13-67
115            android:excludeFromRecents="true"
115-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:54:13-46
116            android:noHistory="true"
116-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:55:13-37
117            android:taskAffinity=".DisconnectVPN"
117-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:56:13-50
118            android:theme="@style/blinkt.dialog" />
118-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:57:13-49
119
120        <service
120-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:59:9-67:19
121            android:name="de.blinkt.openvpn.core.OpenVPNService"
121-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:60:13-65
122            android:exported="true"
122-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:61:13-36
123            android:foregroundServiceType="dataSync|dataSync"
123-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:62:13-53
124            android:permission="android.permission.BIND_VPN_SERVICE" >
124-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:63:13-69
125            <intent-filter>
125-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:64:13-66:29
126                <action android:name="android.net.VpnService" />
126-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:65:17-65
126-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:65:25-62
127            </intent-filter>
128        </service>
129        <service
129-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:68:9-74:19
130            android:name="com.official.fivegfastvpn.MyFirebaseMessagingService"
130-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:69:13-55
131            android:exported="false" >
131-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:70:13-37
132            <intent-filter>
132-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:71:13-73:29
133                <action android:name="com.google.firebase.MESSAGING_EVENT" />
133-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:72:17-78
133-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:72:25-75
134            </intent-filter>
135        </service>
136
137        <meta-data
137-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:76:9-78:70
138            android:name="com.google.android.gms.ads.APPLICATION_ID"
138-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:77:13-69
139            android:value="ca-app-pub-5193340328939721~2015388624" />
139-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:78:13-67
140        <meta-data
140-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:80:9-82:32
141            android:name="com.facebook.sdk.ApplicationId"
141-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:81:13-58
142            android:value="" />
142-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:82:13-29
143
144        <!-- Required: set your sentry.io project identifier (DSN) -->
145        <meta-data
145-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:85:5-159
146            android:name="io.sentry.dsn"
146-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:85:16-44
147            android:value="https://<EMAIL>/4508793236488192" />
147-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:85:45-156
148
149        <!-- enable automatic breadcrumbs for user interactions (clicks, swipes, scrolls) -->
150        <meta-data
150-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:88:5-95
151            android:name="io.sentry.traces.user-interaction.enable"
151-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:88:16-71
152            android:value="true" />
152-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:88:72-92
153        <!-- enable screenshot for crashes (could contain sensitive/PII data) -->
154        <meta-data
154-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:90:5-82
155            android:name="io.sentry.attach-screenshot"
155-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:90:16-58
156            android:value="true" />
156-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:90:59-79
157        <!-- enable view hierarchy for crashes -->
158        <meta-data
158-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:92:5-86
159            android:name="io.sentry.attach-view-hierarchy"
159-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:92:16-62
160            android:value="true" />
160-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:92:63-83
161
162        <!-- enable the performance API by setting a sample-rate, adjust in production env -->
163        <meta-data
163-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:95:5-82
164            android:name="io.sentry.traces.sample-rate"
164-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:95:16-59
165            android:value="1.0" />
165-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:95:60-79
166        <!-- VpnService-based OpenVPN implementation (V2) -->
167        <service
167-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-27:19
168            android:name="de.blinkt.openvpn.core.OpenVPNServiceV2"
168-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-67
169            android:exported="true"
169-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
170            android:foregroundServiceType="dataSync"
170-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-53
171            android:permission="android.permission.BIND_VPN_SERVICE" >
171-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-69
172            <intent-filter>
172-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:64:13-66:29
173                <action android:name="android.net.VpnService" />
173-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:65:17-65
173-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:65:25-62
174            </intent-filter>
175        </service>
176
177        <meta-data
177-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:22:9-24:37
178            android:name="com.google.android.play.billingclient.version"
178-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:23:13-73
179            android:value="7.1.1" />
179-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:24:13-34
180
181        <activity
181-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:26:9-30:75
182            android:name="com.android.billingclient.api.ProxyBillingActivity"
182-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:27:13-78
183            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
183-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:28:13-96
184            android:exported="false"
184-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:29:13-37
185            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
185-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:30:13-72
186        <activity
186-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:31:9-35:75
187            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
187-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:32:13-80
188            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
188-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:33:13-96
189            android:exported="false"
189-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:34:13-37
190            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
190-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:35:13-72
191
192        <receiver
192-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
193            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
193-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
194            android:exported="true"
194-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
195            android:permission="com.google.android.c2dm.permission.SEND" >
195-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
196            <intent-filter>
196-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
197                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
197-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
197-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
198            </intent-filter>
199
200            <meta-data
200-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
201                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
201-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
202                android:value="true" />
202-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
203        </receiver>
204        <!--
205             FirebaseMessagingService performs security checks at runtime,
206             but set to not exported to explicitly avoid allowing another app to call it.
207        -->
208        <service
208-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
209            android:name="com.google.firebase.messaging.FirebaseMessagingService"
209-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
210            android:directBootAware="true"
210-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
211            android:exported="false" >
211-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
212            <intent-filter android:priority="-500" >
212-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:71:13-73:29
213                <action android:name="com.google.firebase.MESSAGING_EVENT" />
213-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:72:17-78
213-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:72:25-75
214            </intent-filter>
215        </service>
216        <service
216-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
217            android:name="com.google.firebase.components.ComponentDiscoveryService"
217-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:55:13-84
218            android:directBootAware="true"
218-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
219            android:exported="false" >
219-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
220            <meta-data
220-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
221                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
221-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
222                android:value="com.google.firebase.components.ComponentRegistrar" />
222-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
223            <meta-data
223-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
224                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
224-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
225                android:value="com.google.firebase.components.ComponentRegistrar" />
225-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
226            <meta-data
226-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b370e0f8072109516ee30527c2197e56\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
227                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
227-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b370e0f8072109516ee30527c2197e56\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
228                android:value="com.google.firebase.components.ComponentRegistrar" />
228-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b370e0f8072109516ee30527c2197e56\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
229            <meta-data
229-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b370e0f8072109516ee30527c2197e56\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
230                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
230-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b370e0f8072109516ee30527c2197e56\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
231                android:value="com.google.firebase.components.ComponentRegistrar" />
231-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b370e0f8072109516ee30527c2197e56\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
232            <meta-data
232-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63aaeacb2a36f7705d7d9290f3343c54\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
233                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
233-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63aaeacb2a36f7705d7d9290f3343c54\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
234                android:value="com.google.firebase.components.ComponentRegistrar" />
234-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63aaeacb2a36f7705d7d9290f3343c54\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
235            <meta-data
235-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
236                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
236-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
237                android:value="com.google.firebase.components.ComponentRegistrar" />
237-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
238            <meta-data
238-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f02bec30df0defe4d8c6fe403d88b9f\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
239                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
239-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f02bec30df0defe4d8c6fe403d88b9f\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
240                android:value="com.google.firebase.components.ComponentRegistrar" />
240-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f02bec30df0defe4d8c6fe403d88b9f\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
241        </service>
242
243        <activity
243-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
244            android:name="com.google.android.gms.common.api.GoogleApiActivity"
244-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
245            android:exported="false"
245-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
246            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
246-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
247        <activity
247-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:20:9-24:75
248            android:name="com.facebook.ads.AudienceNetworkActivity"
248-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:21:13-68
249            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
249-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:22:13-106
250            android:exported="false"
250-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:23:13-37
251            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
251-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:24:13-72
252
253        <provider
253-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:26:9-29:40
254            android:name="com.facebook.ads.AudienceNetworkContentProvider"
254-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:27:13-75
255            android:authorities="com.official.fivegfastvpn.AudienceNetworkContentProvider"
255-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:28:13-82
256            android:exported="false" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
256-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:29:13-37
257        <activity
257-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:73:9-78:43
258            android:name="com.google.android.gms.ads.AdActivity"
258-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:74:13-65
259            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
259-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:75:13-122
260            android:exported="false"
260-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:76:13-37
261            android:theme="@android:style/Theme.Translucent" />
261-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:77:13-61
262
263        <provider
263-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:80:9-85:43
264            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
264-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:81:13-76
265            android:authorities="com.official.fivegfastvpn.mobileadsinitprovider"
265-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:82:13-73
266            android:exported="false"
266-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:83:13-37
267            android:initOrder="100" />
267-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:84:13-36
268
269        <service
269-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:87:9-91:43
270            android:name="com.google.android.gms.ads.AdService"
270-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:88:13-64
271            android:enabled="true"
271-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:89:13-35
272            android:exported="false" />
272-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:90:13-37
273
274        <activity
274-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:93:9-97:43
275            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
275-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:94:13-82
276            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
276-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:95:13-122
277            android:exported="false" />
277-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:96:13-37
278        <activity
278-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:98:9-105:43
279            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
279-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:99:13-82
280            android:excludeFromRecents="true"
280-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:100:13-46
281            android:exported="false"
281-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:101:13-37
282            android:launchMode="singleTask"
282-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:102:13-44
283            android:taskAffinity=""
283-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:103:13-36
284            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
284-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:104:13-72
285
286        <meta-data
286-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:107:9-109:36
287            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
287-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:108:13-79
288            android:value="true" />
288-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:109:13-33
289        <meta-data
289-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:110:9-112:36
290            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
290-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:111:13-83
291            android:value="true" />
291-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:112:13-33
292
293        <provider
293-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
294            android:name="com.google.firebase.provider.FirebaseInitProvider"
294-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
295            android:authorities="com.official.fivegfastvpn.firebaseinitprovider"
295-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
296            android:directBootAware="true"
296-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
297            android:exported="false"
297-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
298            android:initOrder="100" />
298-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
299        <provider
299-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
300            android:name="androidx.startup.InitializationProvider"
300-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
301            android:authorities="com.official.fivegfastvpn.androidx-startup"
301-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
302            android:exported="false" >
302-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
303            <meta-data
303-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
304                android:name="androidx.work.WorkManagerInitializer"
304-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
305                android:value="androidx.startup" />
305-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
306            <meta-data
306-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
307                android:name="androidx.emoji2.text.EmojiCompatInitializer"
307-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
308                android:value="androidx.startup" />
308-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
309            <meta-data
309-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc925df91b3cd356b1aebb6c5f064f8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
310                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
310-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc925df91b3cd356b1aebb6c5f064f8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
311                android:value="androidx.startup" />
311-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc925df91b3cd356b1aebb6c5f064f8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
312            <meta-data
312-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
313                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
313-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
314                android:value="androidx.startup" />
314-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
315        </provider>
316
317        <service
317-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
318            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
318-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
319            android:directBootAware="false"
319-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
320            android:enabled="@bool/enable_system_alarm_service_default"
320-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
321            android:exported="false" />
321-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
322        <service
322-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
323            android:name="androidx.work.impl.background.systemjob.SystemJobService"
323-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
324            android:directBootAware="false"
324-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
325            android:enabled="@bool/enable_system_job_service_default"
325-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
326            android:exported="true"
326-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
327            android:permission="android.permission.BIND_JOB_SERVICE" />
327-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
328        <service
328-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
329            android:name="androidx.work.impl.foreground.SystemForegroundService"
329-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
330            android:directBootAware="false"
330-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
331            android:enabled="@bool/enable_system_foreground_service_default"
331-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
332            android:exported="false" />
332-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
333
334        <receiver
334-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
335            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
335-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
336            android:directBootAware="false"
336-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
337            android:enabled="true"
337-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
338            android:exported="false" />
338-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
339        <receiver
339-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
340            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
340-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
341            android:directBootAware="false"
341-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
342            android:enabled="false"
342-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
343            android:exported="false" >
343-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
344            <intent-filter>
344-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
345                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
345-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
345-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
346                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
346-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
346-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
347            </intent-filter>
348        </receiver>
349        <receiver
349-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
350            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
350-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
351            android:directBootAware="false"
351-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
352            android:enabled="false"
352-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
353            android:exported="false" >
353-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
354            <intent-filter>
354-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
355                <action android:name="android.intent.action.BATTERY_OKAY" />
355-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
355-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
356                <action android:name="android.intent.action.BATTERY_LOW" />
356-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
356-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
357            </intent-filter>
358        </receiver>
359        <receiver
359-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
360            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
360-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
361            android:directBootAware="false"
361-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
362            android:enabled="false"
362-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
363            android:exported="false" >
363-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
364            <intent-filter>
364-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
365                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
365-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
365-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
366                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
366-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
366-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
367            </intent-filter>
368        </receiver>
369        <receiver
369-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
370            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
370-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
371            android:directBootAware="false"
371-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
372            android:enabled="false"
372-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
373            android:exported="false" >
373-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
374            <intent-filter>
374-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
375                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
375-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
375-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
376            </intent-filter>
377        </receiver>
378        <receiver
378-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
379            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
379-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
380            android:directBootAware="false"
380-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
381            android:enabled="false"
381-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
382            android:exported="false" >
382-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
383            <intent-filter>
383-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
384                <action android:name="android.intent.action.BOOT_COMPLETED" />
384-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
384-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
385                <action android:name="android.intent.action.TIME_SET" />
385-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
385-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
386                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
386-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
386-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
387            </intent-filter>
388        </receiver>
389        <receiver
389-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
390            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
390-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
391            android:directBootAware="false"
391-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
392            android:enabled="@bool/enable_system_alarm_service_default"
392-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
393            android:exported="false" >
393-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
394            <intent-filter>
394-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
395                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
395-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
395-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
396            </intent-filter>
397        </receiver>
398        <receiver
398-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
399            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
399-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
400            android:directBootAware="false"
400-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
401            android:enabled="true"
401-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
402            android:exported="true"
402-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
403            android:permission="android.permission.DUMP" >
403-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
404            <intent-filter>
404-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
405                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
405-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
405-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
406            </intent-filter>
407        </receiver>
408
409        <service
409-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
410            android:name="androidx.room.MultiInstanceInvalidationService"
410-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
411            android:directBootAware="true"
411-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
412            android:exported="false" />
412-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
413
414        <uses-library
414-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
415            android:name="androidx.window.extensions"
415-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
416            android:required="false" />
416-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
417        <uses-library
417-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
418            android:name="androidx.window.sidecar"
418-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
419            android:required="false" /> <!-- 'android:authorities' must be unique in the device, across all apps -->
419-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
420        <provider
420-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:12:9-15:40
421            android:name="io.sentry.android.core.SentryInitProvider"
421-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:13:13-69
422            android:authorities="com.official.fivegfastvpn.SentryInitProvider"
422-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:14:13-70
423            android:exported="false" />
423-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:15:13-37
424        <provider
424-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:16:9-20:39
425            android:name="io.sentry.android.core.SentryPerformanceProvider"
425-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:17:13-76
426            android:authorities="com.official.fivegfastvpn.SentryPerformanceProvider"
426-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:18:13-77
427            android:exported="false"
427-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:19:13-37
428            android:initOrder="200" />
428-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:20:13-36
429
430        <uses-library
430-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04acc53f658e9c1c6ef85818b36fbc26\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
431            android:name="android.ext.adservices"
431-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04acc53f658e9c1c6ef85818b36fbc26\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
432            android:required="false" />
432-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04acc53f658e9c1c6ef85818b36fbc26\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
433
434        <meta-data
434-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1aae7574d5734885cc339c3642ba94ef\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
435            android:name="com.google.android.gms.version"
435-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1aae7574d5734885cc339c3642ba94ef\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
436            android:value="@integer/google_play_services_version" />
436-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1aae7574d5734885cc339c3642ba94ef\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
437
438        <receiver
438-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
439            android:name="androidx.profileinstaller.ProfileInstallReceiver"
439-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
440            android:directBootAware="false"
440-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
441            android:enabled="true"
441-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
442            android:exported="true"
442-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
443            android:permission="android.permission.DUMP" >
443-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
444            <intent-filter>
444-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
445                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
445-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
445-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
446            </intent-filter>
447            <intent-filter>
447-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
448                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
448-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
448-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
449            </intent-filter>
450            <intent-filter>
450-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
451                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
451-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
451-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
452            </intent-filter>
453            <intent-filter>
453-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
454                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
454-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
454-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
455            </intent-filter>
456        </receiver>
457
458        <service
458-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
459            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
459-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
460            android:exported="false" >
460-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
461            <meta-data
461-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
462                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
462-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
463                android:value="cct" />
463-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
464        </service>
465        <service
465-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
466            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
466-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
467            android:exported="false"
467-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
468            android:permission="android.permission.BIND_JOB_SERVICE" >
468-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
469        </service>
470
471        <receiver
471-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
472            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
472-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
473            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
473-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
474        <activity
474-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
475            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
475-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
476            android:exported="false"
476-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
477            android:stateNotNeeded="true"
477-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
478            android:theme="@style/Theme.PlayCore.Transparent" />
478-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
479    </application>
480
481</manifest>
