<?php
/**
 * Fix Missing Flag Image
 * Creates placeholder flag images for test servers
 */

require_once 'includes/config.php';

echo "<h1>🏳️ Fix Missing Flag Images</h1>\n";
echo "<p>Creating placeholder flag images for test servers</p>\n";
echo "<hr>\n";

// Create flag directory if it doesn't exist
$flagDir = __DIR__ . '/flag';
if (!is_dir($flagDir)) {
    if (mkdir($flagDir, 0755, true)) {
        echo "<p style='color: #28a745;'>✅ Created flag directory: $flagDir</p>\n";
    } else {
        echo "<p style='color: #dc3545;'>❌ Failed to create flag directory</p>\n";
        exit;
    }
} else {
    echo "<p style='color: #17a2b8;'>ℹ️ Flag directory already exists</p>\n";
}

// Create a simple SVG placeholder for test_working.png
$svgContent = '<?xml version="1.0" encoding="UTF-8"?>
<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
  <rect width="64" height="64" fill="#28a745"/>
  <circle cx="32" cy="32" r="20" fill="#ffffff"/>
  <text x="32" y="38" text-anchor="middle" fill="#28a745" font-family="Arial" font-size="12" font-weight="bold">TEST</text>
</svg>';

// Save as SVG first
$svgPath = $flagDir . '/test_working.svg';
if (file_put_contents($svgPath, $svgContent)) {
    echo "<p style='color: #28a745;'>✅ Created SVG flag: test_working.svg</p>\n";
} else {
    echo "<p style='color: #dc3545;'>❌ Failed to create SVG flag</p>\n";
}

// Try to convert to PNG if ImageMagick is available
if (extension_loaded('imagick')) {
    try {
        $imagick = new Imagick();
        $imagick->readImageBlob($svgContent);
        $imagick->setImageFormat('png');
        $imagick->resizeImage(64, 64, Imagick::FILTER_LANCZOS, 1);
        
        $pngPath = $flagDir . '/test_working.png';
        if ($imagick->writeImage($pngPath)) {
            echo "<p style='color: #28a745;'>✅ Created PNG flag: test_working.png</p>\n";
        } else {
            echo "<p style='color: #ffc107;'>⚠️ Failed to create PNG flag</p>\n";
        }
        $imagick->clear();
    } catch (Exception $e) {
        echo "<p style='color: #ffc107;'>⚠️ ImageMagick error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
} else {
    echo "<p style='color: #ffc107;'>⚠️ ImageMagick not available, PNG conversion skipped</p>\n";
}

// Create a simple HTML-based PNG using GD if available
if (extension_loaded('gd')) {
    try {
        $image = imagecreate(64, 64);
        $green = imagecolorallocate($image, 40, 167, 69);  // Bootstrap success color
        $white = imagecolorallocate($image, 255, 255, 255);
        
        // Fill background
        imagefill($image, 0, 0, $green);
        
        // Draw circle
        imagefilledellipse($image, 32, 32, 40, 40, $white);
        
        // Add text
        $font = 3; // Built-in font
        $text = "TEST";
        $textWidth = imagefontwidth($font) * strlen($text);
        $textHeight = imagefontheight($font);
        $x = (64 - $textWidth) / 2;
        $y = (64 - $textHeight) / 2;
        imagestring($image, $font, $x, $y, $text, $green);
        
        $pngPath = $flagDir . '/test_working.png';
        if (imagepng($image, $pngPath)) {
            echo "<p style='color: #28a745;'>✅ Created PNG flag using GD: test_working.png</p>\n";
        } else {
            echo "<p style='color: #ffc107;'>⚠️ Failed to create PNG using GD</p>\n";
        }
        imagedestroy($image);
    } catch (Exception $e) {
        echo "<p style='color: #ffc107;'>⚠️ GD error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
} else {
    echo "<p style='color: #ffc107;'>⚠️ GD extension not available</p>\n";
}

// Update database to use SVG if PNG creation failed
$flagFile = 'test_working.png';
if (!file_exists($flagDir . '/test_working.png') && file_exists($flagDir . '/test_working.svg')) {
    $flagFile = 'test_working.svg';
    echo "<p style='color: #17a2b8;'>ℹ️ Using SVG flag as fallback</p>\n";
}

// Update the database
try {
    $updateSql = "UPDATE servers SET flagURL = ? WHERE name = 'Working Test Server'";
    $stmt = $conn->prepare($updateSql);
    $stmt->bind_param('s', $flagFile);
    
    if ($stmt->execute()) {
        echo "<p style='color: #28a745;'>✅ Updated database with flag: $flagFile</p>\n";
    } else {
        echo "<p style='color: #dc3545;'>❌ Failed to update database: " . $conn->error . "</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: #dc3545;'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// Test the flag URL
$flagUrl = "http://192.168.0.106/Svpn5g/5GsmartvpnAdminPanel/admin_new/flag/$flagFile";
echo "<h3>Flag URL Test</h3>\n";
echo "<p>Testing flag URL: <a href='$flagUrl' target='_blank'>$flagUrl</a></p>\n";

// Try to load the flag
$headers = @get_headers($flagUrl);
if ($headers && strpos($headers[0], '200') !== false) {
    echo "<p style='color: #28a745;'>✅ Flag URL is accessible</p>\n";
    echo "<img src='$flagUrl' alt='Test Flag' style='border: 1px solid #ccc; margin: 10px 0;'>\n";
} else {
    echo "<p style='color: #ffc107;'>⚠️ Flag URL may not be accessible from web</p>\n";
}

echo "<hr>\n";
echo "<h3>Summary</h3>\n";
echo "<p>Flag image issue should now be resolved. The Android app will no longer show the 404 error for the flag image.</p>\n";

// Add a form to fix the VPN profile validation error
echo "<hr>\n";
echo "<h2>🔧 Fix VPN Profile Validation Error</h2>\n";
echo "<div style='background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0;'>\n";
echo "<h4>❌ Current Issue: VPN profile validation failed: 2132017646</h4>\n";
echo "<p>This error indicates the OpenVPN configuration format is incompatible with Android VPN service.</p>\n";
echo "</div>\n";

echo "<form method='post' style='margin: 20px 0;'>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;'>\n";
echo "<h4>Fix OpenVPN Configuration</h4>\n";
echo "<p>Click below to update the Working Test Server with Android-compatible configuration:</p>\n";
echo "<button type='submit' name='fix_ovpn_config' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>Fix OpenVPN Configuration</button>\n";
echo "</div>\n";
echo "</form>\n";

// Handle the OpenVPN config fix
if (isset($_POST['fix_ovpn_config'])) {
    try {
        // Create Android-compatible OpenVPN configuration
        $androidCompatibleConfig = "client
dev tun
proto udp
remote YOUR_OVHCLOUD_SERVER_IP 1194
resolv-retry infinite
nobind
persist-key
persist-tun
cipher AES-256-CBC
auth SHA256
key-direction 1
verb 3
mute 20

# Inline certificates (Android VPN service requires inline format)
<ca>
-----BEGIN CERTIFICATE-----
# Replace this with your actual CA certificate
# Get this from your OVHcloud OpenVPN server
MIIDQTCCAimgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF
ADA5MQswCQYDVQQGEwJVUzEPMA0GA1UEChMGQW1hem9uMRkwFwYDVQQDExBBbWF6
b24gUm9vdCBDQSAxMB4XDTE1MDUyNjAwMDAwMFoXDTM4MDExNzAwMDAwMFowOTEL
MAkGA1UEBhMCVVMxDzANBgNVBAoTBkFtYXpvbjEZMBcGA1UEAxMQQW1hem9uIFJv
b3QgQ0EgMTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALJ4gHHKeNXj
ca9HgFB0fW7Y14h29Jlo91ghYPl0hAEvrAIthtOgQ3pOsqTQNroBvo3bSMgHFzZM
9O6II8c+6zf1tRn4SWiw3te5djgdYZ6k/oI2peVKVuRF4fn9tBb6dNqcmzU5L/qw
IFAGbHrQgLKm+a/sRxmPUDgH3KKHOVj4utWp+UhnMJbulHheb4mjUcAwhmahRWa6
VOujw5H5SNz/0egwLX0tdHA114gk957EWW67c4cX8jJGKLhD+rcdqsq08p8kDi1L
93FcXmn/6pUCyziKrlA4b9v7LWIbxcceVOF34GfID5yHI9Y/QCB/IIDEgEw+OyQm
jgSubJrIqg0CAwEAAaNCMEAwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0OBBYEFIQYzIU07LwMlJQuCFmcx7IQTgoIMA0GCSqGSIb3DQEBCwUA
A4IBAQCY8jdaQZChGsV2USggNiMOruYou6r4lK5IpDB/G/wkjUu0yKGX9rbxenDI
U5PMCCjjmCXPI6T53iHTfIuJruydjsw2hUwsOBYy4WlzQMVMnPmYXb2oK7xSyQJy
oNQi1VWuSHihrCnLs9w7d7n6b2+g/2v9xtaGm5Deq2ePSNjuwxcJlYcgOTOOBX6O
-----END CERTIFICATE-----
</ca>

<cert>
-----BEGIN CERTIFICATE-----
# Replace this with your actual client certificate
# Get this from your OVHcloud OpenVPN server
MIIDQTCCAimgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF
# ... (client certificate content)
-----END CERTIFICATE-----
</cert>

<key>
-----BEGIN PRIVATE KEY-----
# Replace this with your actual client private key
# Get this from your OVHcloud OpenVPN server
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCydIBxynj
# ... (client private key content)
-----END PRIVATE KEY-----
</key>";

        $updateSql = "UPDATE servers SET configFile = ? WHERE name = 'Working Test Server'";
        $stmt = $conn->prepare($updateSql);
        $stmt->bind_param('s', $androidCompatibleConfig);

        if ($stmt->execute()) {
            echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>\n";
            echo "<h4>✅ OpenVPN Configuration Updated!</h4>\n";
            echo "<p>The configuration has been updated to Android-compatible format.</p>\n";
            echo "<p><strong>IMPORTANT:</strong> You need to replace the placeholder certificates with your actual OVHcloud certificates:</p>\n";
            echo "<ol>\n";
            echo "<li>Get your CA certificate, client certificate, and client key from OVHcloud</li>\n";
            echo "<li>Replace YOUR_OVHCLOUD_SERVER_IP with your actual server IP</li>\n";
            echo "<li>Replace the placeholder certificates in the configuration</li>\n";
            echo "</ol>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0;'>\n";
            echo "<h4>❌ Error</h4>\n";
            echo "<p>Failed to update configuration: " . $conn->error . "</p>\n";
            echo "</div>\n";
        }
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0;'>\n";
        echo "<h4>❌ Error</h4>\n";
        echo "<p>Exception: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "</div>\n";
    }
}

echo "<p><strong>Navigation:</strong></p>\n";
echo "<ul>\n";
echo "<li><a href='setup_local_openvpn.php'>🖥️ Set Up Local OpenVPN Server</a></li>\n";
echo "<li><a href='validate_vpn_servers.php'>🔍 Validate Server Configurations</a></li>\n";
echo "<li><a href='fix_ovhcloud_config.php' style='color: #dc3545; font-weight: bold;'>🔧 Configure OVHcloud Server</a></li>\n";
echo "<li><a href='index.php'>← Back to Admin Panel</a></li>\n";
echo "</ul>\n";
?>
